#pragma once

#if defined(_WIN32) || defined(_WIN64)
#include <windows.h>
#else
#include <pthread.h>
#include "semaphore.h"
#endif



namespace wlthread {

	struct wlmutex {
#if defined(_WIN32) || defined(_WIN64)
		CRITICAL_SECTION sec;
#else
		pthread_mutex_t mutex;
#endif
	};

	bool wlmutex_init(wlmutex* p);
	bool wlmutex_uninit(wlmutex* p);

	void wlmutex_lock(wlmutex* p);
	void wlmutex_unlock(wlmutex* p);

	struct wlevent {
#if defined(_WIN32) || defined(_WIN64)
		HANDLE event;
#else
		sem_t sem;
#endif
	};

	void wlevent_init(wlevent* p);
	void wlevent_uninit(wlevent* p);

	void wlevent_set(wlevent* p);
	unsigned int wlevent_wait(wlevent* p);

	class AutoMutex
	{
	public:
		AutoMutex(wlmutex* _t) : t(_t)
		{
			wlmutex_lock(t);
		}
		~AutoMutex()
		{
			wlmutex_unlock(t);
		}
		wlmutex* t;
	};

};
