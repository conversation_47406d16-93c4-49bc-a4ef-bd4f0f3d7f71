#pragma once
#include "nlohmann/json.hpp"

using json = nlohmann::json;

namespace dev_control_policy
{
    struct Device
    {
        bool is_enabled;

        bool operator==(const Device &other) const;
        bool operator!=(const Device &other) const;
    };

    struct Config
    {
        bool isOpen;
        Device storage_dev;
        Device cdrom;
        Device portable_dev;
        Device wifi_card;

        bool operator==(const Config &other) const;
        bool operator!=(const Config &other) const;
    };

    void from_json(const json &j, Device &d);
    void from_json(const json &j, Config &c);
}
