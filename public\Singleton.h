
#ifndef AFX_SINGLETON_H_INCLUDED
#define AFX_SINGLETON_H_INCLUDED

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
#ifdef _WINDOWS
#include <assert.h>
#endif

// Include additional headers

/////////////////////////////////////////////////////////////////////////////
// CSingletonT class
/////////////////////////////////////////////////////////////////////////////

template <class T>
class CSingletonT
{
private:

	// Attributes

	static T* m_pT;

	// Construction/destruction -- declared as private to prevent outside access

protected:

	CSingletonT() = default;
	virtual ~CSingletonT() = default;

	CSingletonT(const CSingletonT &) = delete;
	CSingletonT(CSingletonT && ) = delete;

	CSingletonT & operator = (const CSingletonT &) = delete;
	CSingletonT & operator = (CSingletonT &&) = delete;

public:

	// Operations

	T* operator->() {  ASSERT(m_pT); return m_pT; }

	static T* GetInstance()
	{
		if (!m_pT) {
			m_pT = new T;
		}

#ifdef _WINDOWS
		assert(m_pT != nullptr);
#endif

		return m_pT;
	}

	static bool HasInstance()
	{
		return m_pT != nullptr;
	}

	static void DestroyInstance()
	{
		delete m_pT;

		m_pT = nullptr;
	}

};

// Initialize singleton static member

template <typename T> T* CSingletonT<T>::m_pT = nullptr;

#endif // #ifndef AFX_SINGLETON_H_INCLUDED

///////////////////////////////////////////////////////////////////////////////
// End of Singleton.h
///////////////////////////////////////////////////////////////////////////////
