
function(AddManifest arg_manifest arg_exe)
	#COMMAND mt.exe -manifest ${arg_manifest} -outputresource:${arg_exe}\;\#1.
	execute_process(
        COMMAND mt.exe -manifest ${arg_manifest} -outputresource:${arg_exe}\;\#1.
    )
endfunction()

function(WinSign args dir)
	
	message("WinSign args=${args} ++++++++++++++")	
	execute_process(COMMAND ${CMAKE_COMMAND} -E remove_directory ${dir})
	execute_process(COMMAND ${CMAKE_COMMAND} -E make_directory ${dir})

	STRING(REGEX REPLACE "!" "\n" args_str "${args}")
	STRING(REGEX MATCHALL "[^\n]+" args_list "${args_str}")
	foreach(item IN LISTS args_list)
		message("item = ${item}  dir = ${dir}")
		execute_process(COMMAND ${CMAKE_COMMAND} -E copy ${item} ${dir} RESULT_VARIABLE result)
		if(NOT result EQUAL 0)
			message(FATAL_ERROR "copy ${item} failed")
		endif()
	endforeach()
	execute_process(
        COMMAND atclienttool.exe ${dir} RESULT_VARIABLE result ERROR_QUIET OUTPUT_QUIET
    )
	foreach(item IN LISTS args_list)
		STRING(REPLACE "/" ";" STR_LIST ${item})
		LIST(GET STR_LIST -1 PE_NAME)		
		execute_process(COMMAND ${CMAKE_COMMAND} -E copy ${dir}/${PE_NAME} ${item} )
	endforeach()	
	message("WinSign ---------------")	
endfunction()

if(WIN32 AND NOT AT_DEBUG)
	if(CALL STREQUAL "AddManifest")
		AddManifest(${ARG1} ${ARG2})
	endif()
endif()

if(WIN32 AND NOT AT_NOSIGN)
	if(CALL STREQUAL "WinSign")
		WinSign(${ARG1} ${ARG2})
	endif()
endif()