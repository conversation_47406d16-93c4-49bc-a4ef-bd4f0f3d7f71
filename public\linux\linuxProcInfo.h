#pragma once
#include <string>
#include <vector>
#include <map>

typedef struct Account {
	std::string userName;
	std::string uid;
	std::string gid;
	Account(std::string& _userName, std::string& _uid, std::string& _gid)
		: userName(_userName), uid(_uid), gid(_gid)
	{
	}
};

typedef struct Proc {
	std::string name;
	std::string state;
	std::string tgid;
	std::string pid;
	std::string ppid;
	std::string uid;
	std::string gid;
	std::string user;
	bool rootpriviledge;
};

class LinuxProcInfo
{
public:
	static void GetAccount(std::vector<Account>& accounts);
	static bool GetProcInfo(pid_t pid, Proc& proc);
	static bool IsMediaPath(const std::string& path);
	static std::vector<std::string> GetAllDisk();
	static std::vector<std::string> GetAllRemovDisk();
	static std::vector<std::pair<std::string, std::string>> GetFilesystemMountedon();
	static std::vector<std::string> GetAllRemovDiskMountedon();
};

