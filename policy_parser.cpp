#include "policy_parser.h"
#include "macro_defs.h"

namespace dev_control_policy
{
    bool Device::operator==(const Device &other) const
    {
        return is_enabled == other.is_enabled;
    }

    bool Device::operator!=(const Device &other) const
    {
        return !(*this == other);
    }

    bool Config::operator==(const Config &other) const
    {
        return isOpen == other.isOpen &&
               storage_dev == other.storage_dev &&
               cdrom == other.cdrom &&
               portable_dev == other.portable_dev &&
               wifi_card == other.wifi_card;
    }

    bool Config::operator!=(const Config &other) const
    {
        return !(*this == other);
    }

    void from_json(const json &j, Device &d)
    {
        j.at("is_enabled").get_to(d.is_enabled);
    }

    void from_json(const json &j, Config &c)
    {
        j.at("isOpen").get_to(c.isOpen);
        j.at(STORAGE_DEV).get_to(c.storage_dev);
        j.at(CDROM_DEV).get_to(c.cdrom);
        j.at(PORTABLE_DEV).get_to(c.portable_dev);
        j.at(WIFI_CARD).get_to(c.wifi_card);
    }
}
