#if defined(WINDOWS_COMPILE)
#include "pch.h"
#include <winerror.h>
#include <wincrypt.h>
#else
#include <thread>
#include <mutex>
#endif

#include "PluginRegister.h"
#include <iostream>
#include <iomanip>
#include "global_defs.h"
#include "utility.h"
#include "macro_defs.h"

//using namespace std::chrono;
// const char* STR_VFIXES_GUID = "{1095278D-2161-46AF-8FB9-2A0D2358323C}";

PluginRegister::PluginRegister(void) : usbprotector_(report_msg_queue_)
{
	this->run_flag_ = true;
	// agent_cfg_ = agent_cfg_parser::GetAgentCfg();
}

PluginRegister::~PluginRegister(void)
{

}

HRESULT PluginRegister::OnAfterInit()
{
	return S_OK;
}

HRESULT PluginRegister::OnBeforeUninit()
{
	return S_OK;
}

HRESULT PluginRegister::OnAfterStart()
{
	QLogInfoUtf8(LOG_NAME, "[Start] usbprotector OnAfterStart call.");

	RotGetObject(CLSID_AgentProxy, GetIID(IAgentProxy), (void**)&sp_agent_);
	if (NULL == this->sp_agent_)
	{
		QLogInfoUtf8(LOG_NAME, "Cannot get CLSID_AgentProxy handle!");
		return E_FAIL;
	}

	this->client_id_ = this->sp_agent_->Register("devc", 20400, this);

	// recv 中心下发的 task policy 线程
	threads_.push_back(std::make_shared<std::thread>(&PluginRegister::HandleTaskOrPolicy, this));

	// 上报数据线程
	threads_.push_back(std::make_shared<std::thread>(&PluginRegister::ReportMsg, this));

	// 开启 u 盘监控
	threads_.push_back(std::make_shared<std::thread>(&USBProtector::Start, &usbprotector_));

	QLogInfoUtf8(LOG_NAME, "[End] usbprotector OnAfterStart call.");
	return S_OK;
}

HRESULT PluginRegister::OnBeforeStop()
{
	if (this->sp_agent_)
		this->sp_agent_->UnRegister(this->client_id_, this);
	run_flag_ = false;
	usbprotector_.Stop();
	return S_OK;
}

void PluginRegister::HandleTaskOrPolicy()
{
	while (run_flag_)
	{
		auto agent_msg = agent_msg_queue_.Pop();
		usbprotector_.HandleAgentProxyMsg(agent_msg.tag, agent_msg.msg);
	}
}

void PluginRegister::ReportMsg()
{
	while (run_flag_)
	{
		auto msg = report_msg_queue_.Pop();
		QLogInfoUtf8(LOG_NAME, "Transfer/Hips: %s", msg.c_str());
		sp_agent_->Report(client_id_, "Transfer/Hips", msg.c_str(), msg.length());
	}
}

int PluginRegister::AgentProxyMsgCall(const char* tag, const char* msg)
{
	agent_msg_queue_.Push(AgentMsg{ tag, msg });
	return 0;
}