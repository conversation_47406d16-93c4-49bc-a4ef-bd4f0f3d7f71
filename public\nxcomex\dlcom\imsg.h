#ifndef _IMSG_H_
#define _IMSG_H_

uvStdComNameSpaceBegin

interface IMsg : public IBase
{
	//////////////////////////////////////////////////////////////////////////
	std_method(SetMsgType)(unsigned int type) PURE;
	std_method_(unsigned int, GetMsgType)() PURE;
	std_method(SetId)(unsigned int id) PURE;
	std_method_(unsigned int, GetId)() PURE;
	std_method(SetVersion)(unsigned int version) PURE;
	std_method_(unsigned int, GetVersion)() PURE;
	std_method(SetLength)(unsigned int len) PURE;
	std_method_(unsigned int, GetLength)() PURE;
	//////////////////////////////////////////////////////////////////////////;
	std_method(SetMsgRawData)(BYTE* data, unsigned int size, unsigned int offset) PURE;
	std_method_(BYTE*, GetMsgRawData)(unsigned int offset) PURE;
	//////////////////////////////////////////////////////////////////////////
	std_method(Clear)() PURE;
	std_method(Attach)(IMsg* pMsg) PURE;
	//////////////////////////////////////////////////////////////////////////

};

// {187EA2E8-A0F2-4DA9-9F1B-F492DCEEF8AD}
_DEFINE_IID(IID_IMsg,
	0x187ea2e8, 0xa0f2, 0x4da9, 0x9f, 0x1b, 0xf4, 0x92, 0xdc, 0xee, 0xf8, 0xad);


interface IMsgProxy : public IBase
{

};

// {8b9e3502-d509-11eb-ac28-5f6601019609}
_DEFINE_IID(IID_IMsgProxy,
	0x357c10f2, 0x8a68, 0x4138, 0xbd, 0xe5, 0x8c, 0x1c, 0x38, 0x96, 0xf7, 0xd5);

interface IMsgPlugin : public IBase
{
	std_method(Send)(REFCLSID sn, REFCLSID tn, UINT msg, IBase* pInMsg, IBase* pOutMsg) PURE;
	std_method(Recv)(REFCLSID sn, REFCLSID tn, UINT msg, IBase* pInMsg, IBase* pOutMsg) PURE;
	std_method(Push)(REFCLSID sn, REFCLSID tn, UINT msg, IBase* pMsg) PURE;
	std_method(Poll)(REFCLSID sn, REFCLSID tn, UINT msg, IBase* pMsg) PURE;
	std_method_(ULONG, GetRoute)() PURE;
	std_method_(ULONG, GetSub)() PURE;
	std_method(Set)(ULONG ad) PURE;
};

// {5F8488ED-8DBA-4C96-9284-B712F846BB09}
_DEFINE_IID(IID_IMsgPlugin,
	0x5f8488ed, 0x8dba, 0x4c96, 0x92, 0x84, 0xb7, 0x12, 0xf8, 0x46, 0xbb, 0x9);

uvStdComNameSpaceEnd

#endif
