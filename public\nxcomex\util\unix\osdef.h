#ifndef _UTIL_OSDEF_H_
#define _UTIL_OSDEF_H_

#include <stdbool.h>
#include <dlfcn.h>

#define CALLBACK    PASCAL
#define WINAPI      CDECL
#define WINAPIV     CDECL
#define APIENTRY    WINAPI
#define APIPRIVATE  CDECL
#ifdef _68K_
#define PASCAL      __pascal
#else
#define PASCAL
#endif
#elif _MSC_VER

#ifndef WINAPI
#define WINAPI __stdcall
#endif

#ifndef CALLBACK
#define CALLBACK __stdcall
#endif

#ifndef WINAPIV
#define WINAPIV     __cdecl
#endif 

#ifndef APIENTRY
#define APIENTRY    WINAPI
#endif 



#ifndef APIPRIVATE
#define APIPRIVATE  __stdcall
#endif 


#ifndef PASCAL
#define PASCAL      __stdcall
#endif 


#endif
