#pragma once
#include <fstream>
#include <string>
#include "nlohmann/json.hpp"

namespace agent_cfg_parser
{
    struct Log {
        int level;
        int count;
        std::string path;
        std::string size;
        int expired;
    };

    struct Server {
        int interval;
        std::string addr;
        std::string protocol;
    };

    struct Proxy {
        int port;
    };

    struct Plugins {
        int listen;
    };

    struct Extends {
        std::string avl;
        std::string gproxy;
        std::string interact;
        std::string mladdr;
        std::string rasp;
        std::string staddr;
        std::string static_url;
        std::string update;
        std::string upload;
        std::string vul;
    };

    struct Config {
        std::string guid;
        std::string hostname;
        std::string ip;
        bool daemon;
        std::string installdir;
        std::string tenant;
        std::string uninstall;
        int socketport;
        int dispatcherport;
        std::string collaborativeprojects;
        Log log;
        Server server;
        Proxy proxy;
        Plugins plugins;
        Extends extends;
    };

    void from_json(const nlohmann::json& j, Log& log);
    void from_json(const nlohmann::json& j, Server& server);
    void from_json(const nlohmann::json& j, Proxy& proxy);
    void from_json(const nlohmann::json& j, Plugins& plugins);
    void from_json(const nlohmann::json& j, Extends& extends);
    void from_json(const nlohmann::json& j, Config& config);

    inline struct Config GetAgentCfg()
    {
#if defined(_WIN32)
        std::string path = "C:\\Program Files\\acs\\conf\\agent.cfg";
#else
        std::string path = "/opt/acs/conf/agent.cfg";
#endif
        try
        {
            std::ifstream read_json(path);
            nlohmann::json jroot = nlohmann::json::parse(read_json);
            return jroot.get<Config>();
        }
        catch (...)
        {
            return Config{};
        }
    }
}
