#ifndef _ICOMBASE_H_
#define _ICOMBASE_H_

#include <dlcom/unknown.h>

uvStdComNameSpaceBegin

//////////////////////////////////////////////////////////////////////////
interface ICompoentLoader : public IBase
{
	std_method(CreateInstance)(IBase* rot, REFCLSID clsid, IBase* punk, REFIID iid, void **ppv) PURE;
	std_method_(LPCSTR, ProgIDFromCLSID)(REFCLSID clsid) PURE;
	std_method_(CLSID, CLSIDFromProgID)(LPCSTR lpProgId) PURE;
};

interface ILibManager : public IBase
{
	std_method(Load)(LPSTRING path, LPCSTR name) PURE;
	std_method(Free)() PURE;
	std_method_(DWORD, GetCount)() PURE;
};


// {E670ECA1-E73C-4EE7-92D6-42C597254A38}
_DEFINE_IID(IID_ICompoentLoader,
	0xe670eca1, 0xe73c, 0x4ee7, 0x92, 0xd6, 0x42, 0xc5, 0x97, 0x25, 0x4a, 0x38);

// {619CDF0D-DEA4-4A4E-8774-D2D51BE0B208}
_DEFINE_IID(IID_ILibManager,
	0x619cdf0d, 0xdea4, 0x4a4e, 0x87, 0x74, 0xd2, 0xd5, 0x1b, 0xe0, 0xb2, 0x8);

// {6D9D2CF3-276A-426e-9041-FB5428DE44B1}
_DEFINE_GUID(CLSID_CObjectLoader,
	0x6d9d2cf3, 0x276a, 0x426e, 0x90, 0x41, 0xfb, 0x54, 0x28, 0xde, 0x44, 0xb1);


//////////////////////////////////////////////////////////////////////////
interface IComRunningObjectTable : public IBase
{
	std_method(CreateInstance)(IBase* rot, REFCLSID clsid, IBase* punk, REFIID iid, void **ppv) PURE;
	std_method(Register)(REFCLSID clsid, LPCSTR progId, IBase* punk, ULONG id) PURE;
	std_method(Revoke)(REFCLSID clsid) PURE;
	std_method(IsRunning)(REFCLSID clsid) PURE;
	std_method(GetObject)(REFCLSID clsid, REFCLSID iid, IBase** ppunk) PURE;
	std_method(RevokeAll)() PURE;
	std_method_(LPCSTR, ProgIDFromCLSID)(REFCLSID clsid) PURE;
	std_method_(CLSID, CLSIDFromProgID)(LPCSTR lpProgId) PURE;
	std_method_(UINT, GetObjectCount)() PURE;
};

// {08F56552-D015-4C3B-B984-2211A4F880FF}
_DEFINE_IID(IID_IComRunningObjectTable,
	0x8f56552, 0xd015, 0x4c3b, 0xb9, 0x84, 0x22, 0x11, 0xa4, 0xf8, 0x80, 0xff);

//////////////////////////////////////////////////////////////////////////
interface IComRotMessage : public IComRunningObjectTable
{
	std_method(CreateMessage)(IBase** pMsg) PURE;
	std_method(SendMessage)(REFCLSID sn, REFCLSID tn, ULONG gp, UINT msg, IBase* pMsg) PURE;
	std_method(PostMessage)(REFCLSID sn, REFCLSID tn, ULONG gp, UINT msg, IBase* pMsg) PURE;
};

// {4EBB368E-462C-4FF9-BEA4-71D843E8AB1B}
_DEFINE_IID(IID_IComRotMessage,
	0x4ebb368e, 0x462c, 0x4ff9, 0xbe, 0xa4, 0x71, 0xd8, 0x43, 0xe8, 0xab, 0x1b);


//////////////////////////////////////////////////////////////////////////

// {513F39CB-04C7-4068-82DA-FAFE689D5EE4}
_DEFINE_GUID(ClSID_CComRunningObjectTable,
	0x513f39cb, 0x4c7, 0x4068, 0x82, 0xda, 0xfa, 0xfe, 0x68, 0x9d, 0x5e, 0xe4);

//////////////////////////////////////////////////////////////////////////

#define OBJECT_ERROR			1
#define OBJECT_RUN_RET_SUCCESS	1000
#define OBJECT_RUN_RET_RESTART	1001
#define OBJECT_RUN_RET_FAILD	2000
#define OBJECT_RUN_RET_NULL		2001
#define OBJECT_RUN_RET_CLSIDERR 2002
#define OBJECT_RUN_RET_COMPERR	2003
#define OBJECT_RUN_RET_FILEERR	2004
#define OBJECT_RUN_RET_MEMERR	2005
#define OBJECT_RUN_RET_ROTERR	2006
#define OBJECT_RUN_RET_PARAMERR 2007
#define OBJECT_RUN_RET_STARTERR 2008

interface IExit : public IBase
{
	std_method(OnExit)(UINT uExit) PURE;
	std_method_(UINT, GetExitCode)() PURE;
};

// {70B0D10B-463F-496A-90A5-F22175F77A1D}
_DEFINE_IID(IID_IExit,
	0x70b0d10b, 0x463f, 0x496a, 0x90, 0xa5, 0xf2, 0x21, 0x75, 0xf7, 0x7a, 0x1d);


interface IObjectRun : public IBase
{
	std_method(RegisterCode)(LPCSTR buf, ULONG ulen) PURE;
	std_method_(LPCSTR, GetRegisterCode)() PURE;
	std_method(SetPath)(LPSTRING run, UINT len) PURE;
	std_method_(LPSTRING, GetPath)() PURE;
	//////////////////////////////////////////////////////////////////////////
	std_method(Init)(HINSTANCE instance, basic_tchar* argv[], int argc) PURE;
	std_method(Start)(HINSTANCE instance, UINT type) PURE;
	std_method(Stop)(HINSTANCE instance, UINT type,UINT exit) PURE;
	std_method_(UINT, Run)(HINSTANCE instance, UINT type) PURE;
	std_method(NotifyExit)(HINSTANCE instance, UINT exit) PURE;
	std_method(Uninit)(HINSTANCE instance) PURE;
	std_method(Clear)() PURE;
	//////////////////////////////////////////////////////////////////////////
	std_method(GetRot)(IBase** Base) PURE;
	std_method(GetObjectLoader)(IBase** Base) PURE;
	std_method_(CLSID, GetRunPluginCLSID)() PURE;
	std_method_(UINT, GetRunPluginsCount)() PURE;
	std_method_(UINT, GetExitCode)() PURE;
};


// {321B84B2-ACE3-4EC4-9E0C-A63870839F07}
_DEFINE_IID(IID_IObjectRun,
	0x321b84b2, 0xace3, 0x4ec4, 0x9e, 0xc, 0xa6, 0x38, 0x70, 0x83, 0x9f, 0x7);

// {7DADD097-97B4-45ec-A04C-135604FB6934}
_DEFINE_GUID(CLSID_CObjectRun,
	0x7dadd097, 0x97b4, 0x45ec, 0xa0, 0x4c, 0x13, 0x56, 0x4, 0xfb, 0x69, 0x34);

uvStdComNameSpaceEnd


#endif
