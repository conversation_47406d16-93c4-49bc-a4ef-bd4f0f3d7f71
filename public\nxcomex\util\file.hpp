#ifndef _UTIL_FILE_HPP_
#define _UTIL_FILE_HPP_

#include <util/core.hpp>

typedef unsigned long	file_path_len;
typedef const char*		file_path;


#if (TARGET_OS == OS_WINDOWS)
    typedef HANDLE              _fd_t;
	typedef	long long			_fd_size;
    #define FILE_HANDLE		    HANDLE
    #define FILE_INVALID	    INVALID_HANDLE_VALUE
#elif (TARGET_OS == OS_POSIX)
    typedef int                 _fd_t;
	typedef	long				_fd_size;
    #define	FILE_HANDLE		    int
    #define	FILE_INVALID	    (int) -1
#elif (TARGET_OS == OS_DARWIN)
    typedef int                 _fd_t;
	typedef	long				_fd_size;
    #define	FILE_HANDLE		    int
    #define	FILE_INVALID	    (int) -1
#endif



#endif


