#ifndef _UNKNOWN_H_
#define _UNKNOWN_H_

#include <util/util.h>
#include <dlcom/id.hpp>

#ifdef __cplusplus

#define uvStdComNameSpaceBegin						namespace StdCom {
#define uvStdComNameSpaceEnd						}
#define uvStdComNameSpace							using namespace StdCom;

#else 

#define uvStdComlNameSpaceBegin	
#define uvStdComNameSpaceEnd		
#define uvStdComNameSpace			

#endif



uvStdComNameSpaceBegin


#if defined(__cplusplus)


interface IBase
{
	std_method(QueryInterface)(REFIID riid, void **ppv) PURE;
	std_method_(ULONG, AddRef)() PURE;
	std_method_(ULONG, Release)() PURE;
};

#else

typedef struct IBaseUnknownVtbl
{
	HRESULT(OS_STDCALL *QueryInterface)(IUnknown * This, REFIID riid, void **ppvObject);
	ULONG(OS_STDCALL *AddRef)(IUnknown * This);
	ULONG(OS_STDCALL *Release)(IUnknown * This);
} IBaseUnknownVtbl;

interface IBaseUnknown
{
	CONST_VTBL struct IBaseUnknownVtbl *lpVtbl;
};


#define IBaseUnknown_QueryInterface(This,riid,ppvObject)	\
    ( (This)->lpVtbl -> QueryInterface(This,riid,ppvObject) ) 

#define IBaseUnknown_AddRef(This)	\
    ( (This)->lpVtbl -> AddRef(This) ) 

#define IBaseUnknown_Release(This)	\
    ( (This)->lpVtbl -> Release(This) ) 


#endif

// {00000000-0000-0000-0000-000000000046}
_DEFINE_IID(IID_IBase,
	0x00000000, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46);


#if defined(__cplusplus)


interface IComClassFactory : public IBase
{
	std_method_(HRESULT, CreateInstance)(IBase *pUnkOuter, REFIID riid, void **ppvObject) PURE;
	std_method_(HRESULT, LockServer)(int fLock) PURE;
};

#else

interface IComClassFactoryVtbl
{
	
	HRESULT(OS_STDCALL *QueryInterface)(
		IComClassFactory * This, REFIID riid,void **ppvObject);
	ULONG(OS_STDCALL *AddRef)(IComClassFactory * This);

	ULONG(OS_STDCALL *Release)(IComClassFactory * This);

	HRESULT(OS_STDCALL *CreateInstance)(
		IComClassFactory * This,IUnknown *pUnkOuter,REFIID riid, void **ppvObject);

	HRESULT(OS_STDCALL *LockServer)(IComClassFactory * This, BOOL fLock);

} IComClassFactoryVtbl;

interface IComClassFactory
{
	CONST_VTBL struct IComClassFactoryVtbl *lpVtbl;
};

#define IComClassFactory_QueryInterface(This,riid,ppvObject)	\
    ( (This)->lpVtbl -> QueryInterface(This,riid,ppvObject) ) 

#define IComClassFactory_AddRef(This)	\
    ( (This)->lpVtbl -> AddRef(This) ) 

#define IComClassFactory_Release(This)	\
    ( (This)->lpVtbl -> Release(This) ) 


#define IComClassFactory_CreateInstance(This,pUnkOuter,riid,ppvObject)	\
    ( (This)->lpVtbl -> CreateInstance(This,pUnkOuter,riid,ppvObject) ) 

#define IComClassFactory_LockServer(This,fLock)	\
    ( (This)->lpVtbl -> LockServer(This,fLock) ) 


#endif

// {00000001-0000-0000-0000-000000000046}
_DEFINE_IID(IID_IComClassFactory,
	0x000000001, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46);


// {00000000-0000-0000-0000-000000000000}
_DEFINE_IID(COMPONENT_NULL,
	0x00000000, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00);

#define INULL									((IBase*)0)

uvStdComNameSpaceEnd


#endif

