#ifndef _COMPAT_MSGHDR_H_
#define _COMPAT_MSGHDR_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif


#if (TARGET_OS == OS_WINDOWS)

	
	#define IOV_MAX 255

	struct iovec
	{
		void  *iov_base;
		size_t iov_len;
	};

	struct msghdr
	{
		void         *msg_name;
		size_t        msg_namelen;
		struct iovec *msg_iov;
		size_t        msg_iovlen;
		void         *msg_control;
		size_t        msg_controllen;
		int           msg_flags;
	};

	ssize_t sendmsg(int fd, const struct msghdr *msg, int flags);
	ssize_t recvmsg(int fd, struct msghdr *msg, int flags);

#endif

#ifdef	__cplusplus
}
#endif

#endif

