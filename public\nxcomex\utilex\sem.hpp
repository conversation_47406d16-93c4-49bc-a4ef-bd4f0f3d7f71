#ifndef _UTILEX_SEM_HPP_
#define _UTILEX_SEM_HPP_

#include <utilex/crt.hpp>

#ifdef __cplusplus

class CSemHandle
{
public:
	CSemHandle() {
		_sem_init(&this->m_h, 0);
	}
	~CSemHandle()  {
		Close();
	}


	CSemHandle& operator=(CSemHandle& h) {

		if (this != &h)
		{
			Close();
			Attach(h.<PERSON>ach());
		}
		return *this;
	}

	operator _sem_t() {
		return(m_h);
	}

	void Attach(_sem_t h) {
		m_h = h;
	}

	_sem_t Detach() {

		_sem_t h;
		h = m_h;
		return(h);
	}

	std_method_impl Close(){
		_sem_destroy(&this->m_h);
		return S_OK;
	}

	std_method_impl Wait() {
		_sem_wait(&this->m_h);
		return S_OK;
	}

	std_method_impl WaitTime(int sec) {

		sleep_time(sec);
		return S_OK;
	}

	std_method_impl Post() {
		_sem_post(&this->m_h);
		return S_OK;
	}

public:
	_sem_t m_h;
};



#endif


#endif
