#ifndef _COMPAT_COND_H_
#define _COMPAT_COND_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif

int		_cond_init(_cond_t* cond);
void	_cond_destroy(_cond_t* cond);
void	_cond_signal(_cond_t* cond);
void	_cond_broadcast(_cond_t* cond);
void	_cond_wait(_cond_t* cond, _mutex_t* mutex);
int		_cond_timedwait(_cond_t* cond, _mutex_t* mutex, unsigned long timeout);


#ifdef	__cplusplus
}
#endif

#endif


