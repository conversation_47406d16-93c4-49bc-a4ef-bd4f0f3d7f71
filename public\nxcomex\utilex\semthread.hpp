#ifndef _UTILEX_SEMTHREAD_HPP_
#define _UTILEX_SEMTHREAD_HPP_

#include <utilex/crt.hpp>
#include <utilex/thread.hpp>
#include <utilex/sem.hpp>

#ifdef __cplusplus

	class isemthread : ithread
	{
	public:
		isemthread() {

			m_Exit = 0;
		}
		virtual ~isemthread() {
	
		}

	public:
		std_method_(int, Start_Thread)() {

			return ithread::Start_Thread();
		}

		std_method_(int, Stop_Thread)() {

			Exit_Thread();
			this->m_SemPost.Post();
			return ithread::Stop_Thread();
		}
		std_method(PostSem)() {

			this->m_SemPost.Post();
			return S_OK;
		}

		std_method(Exit_Thread)() {

			SYNC_OBJ(&m_csLock);
			m_Exit = 0;
			return S_OK;
		}

	protected:
		std_method(Thread_Init)() {

			return S_OK;
		}
		std_method(Thread_Run)() {
			
			while (!m_Exit)
			{
				this->m_SemPost.Wait();
				rc_assert_break(m_Exit == 0)
				this->Sem_Thread_Run();
			}

			return S_OK;
		}
		std_method(Thread_UnInit)() {

			return S_OK;
		}

	protected:
		std_method(Sem_Thread_Run)() PURE;
	protected:
		CAutoLock		m_csLock;
		CSemHandle		m_SemPost;
		UINT			m_Exit;
	};

#endif


#endif
