#ifndef _UTILEX_SENTRY_HPP_
#define _UTILEX_SENTRY_HPP_


#include <util/util.h>

#ifdef __cplusplus

	struct default_sentry
	{
		static void* default_value() 
		{ 
			return 0; 
		}
		template<class _Ptr> static bool equal_to(_Ptr l, _Ptr r) 
		{ 
			return l == r;
		}
		template<class _Ptr> static void destroy(_Ptr p) 
		{
			if (p != NULL) {
				delete p;
				p = NULL;
			}
		}
	};

	struct default_array_sentry
	{
		static void* default_value() 
		{ 
			return 0; 
		}
		template<class _Ptr> static bool equal_to(_Ptr l, _Ptr r) 
		{ 
			return l == r; 
		}
		template<class _Ptr> static void destroy(_Ptr p) 
		{ 
			if (p != NULL) {
				delete []p;
				p = NULL;
			}
		}
	};

	#if(TARGET_OS == OS_WINDOWS)

		struct hmodule_sentry
		{
			static HMODULE default_value() { return 0; }
			static bool equal_to(HMODULE l, HMODULE r) { return l == r; }
			static void destroy(HMODULE mod) { 
				if (mod)
					::FreeLibrary(mod);
			}
		};

		struct handle_sentry
		{
			static HANDLE default_value() { return 0; }
			static bool equal_to(HANDLE l, HANDLE r) { return l == r; }
			static void destroy(HANDLE handle) { 
				if (handle)
					::CloseHandle(handle);
			}
		};

		struct file_handle_sentry
		{
			static HANDLE default_value() { return INVALID_HANDLE_VALUE; }
			static bool equal_to(HANDLE l, HANDLE r) { return l == r; }
			static void destroy(HANDLE h) { 
				if (INVALID_HANDLE_VALUE != h) 
					::CloseHandle(h); 
			}
		};

		struct find_handle_sentry
		{
			static HANDLE default_value() { return INVALID_HANDLE_VALUE; }
			static bool equal_to(HANDLE l, HANDLE r) { return l == r; }
			static void destroy(HANDLE h) { 
				if (INVALID_HANDLE_VALUE != h) 
					::FindClose(h); 
			}
		};

		struct socket_handle_sentry
		{
			static _sock_t default_value() { return INVALID_SOCKET; }
			static bool equal_to(_sock_t l, _sock_t r) { return l == r; }
			static void destroy(_sock_t h) { 
				if (INVALID_SOCKET != h) 
					::closesocket(h); 
			}
		};

		struct FileMapping_sentry
		{
			static void* default_value(){ return 0; }
			static bool equal_to(HANDLE l, HANDLE r){ return l == r; }
			static void destroy(HANDLE p){
				if (p != NULL) 
					::UnmapViewOfFile(p);
			}
		};


	#elif(TARGET_OS == OS_POSIX)

		struct hmodule_sentry
		{
			static HMODULE default_value() { return 0; }
			static bool equal_to(HMODULE l, HMODULE r) { return l == r; }
			static void destroy(HMODULE h) { if (h) dlclose(h); }
		};

		struct socket_handle_sentry
		{
			static _sock_t default_value() { return INVALID_SOCKET; }
			static bool equal_to(_sock_t l, _sock_t r) { return l == r; }
			static void destroy(_sock_t h) { if (INVALID_SOCKET != h) ::close(h); }
		};

	#elif(TARGET_OS == OS_DARWIN)

	#endif

	template<class _Ptr, class _Traits = default_sentry>
    class sentry
	{
	public:
		sentry()
		{
			m_p = (_Ptr)m_tr.default_value(); 
		}
		sentry(_Ptr p) : m_p(p)
		{

		}

		sentry(const sentry& other)
		{
			m_p = other.m_p;
		}
		~sentry() 
		{ 
			m_tr.destroy(m_p); 
		}
		sentry& operator = (_Ptr p) 
		{ 
			if(!m_tr.equal_to(m_p, p)) 
			{ 
				m_tr.destroy(m_p); m_p = p; 
			} 
			return *this; 
		}
		_Ptr detach() 
		{ 
			_Ptr tmp = m_p; 
			m_p = (_Ptr)m_tr.default_value(); 
			return tmp; 
		}
		void dispose()
		{ 
			m_tr.destroy(m_p); 
			m_p = (_Ptr)m_tr.default_value(); 
		}
		operator _Ptr () const 
		{ 
			return m_p; 
		}
		_Ptr operator -> () const 
		{ 
			return m_p; 
		}

		_Ptr ptr() 
		{ 
			return m_p; 
		}
		const _Ptr ptr() const 
		{ 
			return m_p;
		}

	public:
		_Ptr m_p;
		_Traits m_tr;
	};


	#define UCharArrayPtr	sentry<unsigned char*, default_array_sentry>
	#define CharArrayPtr	sentry<char*, default_array_sentry>
	#define DynamiclibPtr	sentry<HMODULE, hmodule_sentry>

#endif


#endif
