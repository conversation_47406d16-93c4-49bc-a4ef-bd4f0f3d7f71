// FileZilla - a Windows ftp client

// Copyright (C) 2004 - <PERSON> <<EMAIL>>

// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version 2
// of the License, or (at your option) any later version.

// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.

// OptionsTypePage.cpp: Implementierungsdatei
//

#pragma once
#pragma message("Using include dir's ExceptionReport")
#ifndef RSExecptionTString
#include <string>
typedef std::basic_string<TCHAR> RSExecptionTString;
#endif

class CExceptionReport
{
public:
	CExceptionReport();
	~CExceptionReport();

	static LONG WINAPI UnhandledExceptionFilter(PEXCEPTION_POINTERS pExceptionInfo);

private:

	static const RSExecptionTString FormatCurrentTimeString();

	static bool writeMiniDump(PEXCEPTION_POINTERS pExceptionInfo);
	static bool writeSelfExecption(PEXCEPTION_POINTERS pExceptionInfo);

	static void SuspendThreads();


	static LPTOP_LEVEL_EXCEPTION_FILTER m_previousExceptionFilter;
	static TCHAR m_pDmpFileName[MAX_PATH];
	static TCHAR m_pExDmpFileName[MAX_PATH];
	static HANDLE m_hDumpFile;
	
	static BOOL m_bFirstRun;

	static void Logmsg(LPCTSTR lpFormat, ...)
	{
		if(_tcslen(m_pExDmpFileName) <= 0)
			return;
		if (!lpFormat || _tcslen(lpFormat) == 0)
			return;

		FILE* fp = NULL;
		_tfopen_s(&fp, m_pExDmpFileName, _T("a+"));
		if(NULL == fp)
			return;

		va_list args;
		va_start( args, lpFormat );

		_vftprintf_s(fp, lpFormat, args);
		_ftprintf(fp, _T("\r\n"));
		va_end(args);

		fclose(fp);
		fp = NULL;
	}
};


extern CExceptionReport ExceptionReport; //global instance of class




