#ifndef _COMPAT_TIME_H_
#define _COMPAT_TIME_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif

#if (TARGET_OS == OS_WINDOWS)
	 int _gettimeofday(struct timeval * tp, struct timezone * tzp);
#endif

#if (TARGET_OS != OS_WINDOWS)
	 unsigned long GetTickCount();
#endif


 struct tm * _localtime_t(const time_t *timep, struct tm *result);

 int get_time_t(char* tm, unsigned long len);

#ifdef	__cplusplus
}
#endif

#endif

