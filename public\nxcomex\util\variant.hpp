#ifndef _UTIL_VARIANT_HPP_
#define _UTIL_VARIANT_HPP_

#include <util/util.h>


#ifdef __cplusplus
extern "C"
{
#endif

#pragma pack(1)

enum var_enum_t
{
	VET_EMPTY	= 0,
	VET_NULL	= 1,

	VET_I1		= 2,
	VET_I2		= 3,
	VET_I4		= 4,
	VET_I8		= 5,

	VET_UI1		= 6,
	VET_UI2		= 7,
	VET_UI4		= 8,
	VET_UI8		= 9,

	VET_R4		= 10,
	VET_R8		= 11,

	VET_INT		= 12,
	VET_UINT	= 13,

	VET_LPSTR	= 14,
	VET_LPWSTR	= 15,

	VET_VOID	= 20

};

//////////////////////////////////////////////////////////////////////////


/*union {
		*    CHAR           VET_I1
		*    SHORT          VET_I2
		*    LONG           VET_I4
		*	 LONGLONG       VET_I8
		*    BYTE           VET_UI1
		*    USHORT         VET_UI2
		*    ULONG          VET_UI4
		*    ULONGLONG      VET_UI8
		*    FLOAT          VET_R4
		*    DOUBLE         VET_R8
		*    INT            VET_INT
		*    UINT           VET_UINT
		*	 void*			VET_VOID
*/

typedef unsigned short var_type;
typedef unsigned short provarv1;
typedef unsigned short provarv2;
typedef unsigned short provarv3;

typedef struct tag_varaint_t
{
	var_type vt;
	provarv1 v1;
	provarv2 v2;
	provarv3 v3;
	union 
	{
		char			cVal;
		short			iVal;
		long			lVal;
		int64_t			llVal;

		unsigned char	uVal;
		unsigned short	uiVal;
		unsigned long	ulVal;
		uint64_t		ullVal;
		
		float			fltVal;
		double			dblVal;
	
		int				intVal;
		unsigned int	uintVal;

		char*			pszVal;
		wchar_t*		pwszVal;

		void*			pVal;
	};
}varaint_t;

#pragma pack()

#ifdef __cplusplus
}
#endif

#endif // duplicate inclusion protection

