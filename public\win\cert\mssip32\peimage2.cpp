//+-------------------------------------------------------------------------
//
//  Microsoft Windows
//
//  Copyright (C) Microsoft Corporation, 1996 - 1999
//
//  File:       peimage2.cpp
//
//  Contents:   Microsoft SIP Provider
//
//  History:    14-Mar-1997 p<PERSON><PERSON>   created
//
//--------------------------------------------------------------------------


#include    "global.hxx"

inline DWORD AlignIt (DWORD Value, DWORD Alignment) { return (Value + (Alignment - 1)) & ~(Alignment -1); }

#define InitializeListHead(ListHead) (\
    (ListHead)->Flink = (ListHead)->Blink = (ListHead))

#define MAP_READONLY  TRUE
#define MAP_READWRITE FALSE


BOOL
CalculateImagePtrs(
    PLOADED_IMAGE LoadedImage
    )
{
    PIMAGE_DOS_HEADER DosHeader;
    BOOL fRC = FALSE;

    // Everything is mapped. Now check the image and find nt image headers

    __try {
        DosHeader = (PIMAGE_DOS_HEADER)LoadedImage->MappedAddress;

        if ((DosHeader->e_magic != IMAGE_DOS_SIGNATURE) &&
            (DosHeader->e_magic != IMAGE_NT_SIGNATURE)) {
            __leave;
        }

        if (DosHeader->e_magic == IMAGE_DOS_SIGNATURE) {
            if (DosHeader->e_lfanew == 0) {
                __leave;
            }
            LoadedImage->FileHeader = (PIMAGE_NT_HEADERS)((ULONG_PTR)DosHeader + DosHeader->e_lfanew);

            if (
                // If IMAGE_NT_HEADERS would extend past the end of file...
                (PBYTE)LoadedImage->FileHeader + sizeof(IMAGE_NT_HEADERS) >
                    (PBYTE)LoadedImage->MappedAddress + LoadedImage->SizeOfImage ||

                // ..or if it would begin in, or before the IMAGE_DOS_HEADER...
                (PBYTE)LoadedImage->FileHeader <
                    (PBYTE)LoadedImage->MappedAddress + sizeof(IMAGE_DOS_HEADER)  )
            {
                // ...then e_lfanew is not as expected.
                // (Several Win95 files are in this category.)
                __leave;
            }
        } else {

            // No DOS header indicates an image built w/o a dos stub

            LoadedImage->FileHeader = (PIMAGE_NT_HEADERS)DosHeader;
        }

        if ( LoadedImage->FileHeader->Signature != IMAGE_NT_SIGNATURE ) {
            __leave;
        }

        // No optional header indicates an object...

        if ( !LoadedImage->FileHeader->FileHeader.SizeOfOptionalHeader ) {
            __leave;
        }

        // Check for versions < 2.50

        if ( LoadedImage->FileHeader->OptionalHeader.MajorLinkerVersion < 3 &&
             LoadedImage->FileHeader->OptionalHeader.MinorLinkerVersion < 5 ) {
            __leave;
        }

        InitializeListHead( &LoadedImage->Links );
        LoadedImage->NumberOfSections = LoadedImage->FileHeader->FileHeader.NumberOfSections;
        LoadedImage->Sections = IMAGE_FIRST_SECTION(LoadedImage->FileHeader);
        fRC = TRUE;

    } __except ( EXCEPTION_EXECUTE_HANDLER ) { }

    return fRC;
}

const DWORD MAP_IT_BUF_SIZE = 4 * 1024 * 1024;
BOOL ReadFileBlock(HANDLE hFile, LPVOID buf, const DWORD cbToRead, DWORD& cbLoaded, BOOL bSameSize = TRUE, DWORD bufSize = MAP_IT_BUF_SIZE)
{
	DWORD cbReaded = 0;
	if ( !ReadFile(hFile, buf, cbToRead, &cbReaded, NULL)
		|| ( bSameSize && cbReaded != cbToRead ) )
	{
		return FALSE;
	}
	cbLoaded += cbReaded;
	return TRUE;
}
DWORD_PTR mssip_ImageDirectoryEntryToData(
	LOADED_IMAGE& Image,
	__in USHORT DirectoryEntry,
	__out PULONG Size
	);

PVOID WINAPI RtlImageRvaToVa( const IMAGE_NT_HEADERS *nt, HMODULE module, DWORD rva, IMAGE_SECTION_HEADER **section );

BOOL
MapIt(
    HANDLE hFile,
    PLOADED_IMAGE LoadedImage
    )
{
	//HANDLE hMappedFile;

	PIMAGE_DOS_HEADER DosHeader;
	BOOL fRC = FALSE;

	DWORD cbLoaded = 0;

	ZeroMemory(LoadedImage, sizeof(*LoadedImage));

	// Everything is mapped. Now check the image and find nt image headers

	__try {
		LoadedImage->SizeOfImage = GetFileSize(hFile, NULL);
		SetFilePointer(hFile, 0, NULL, FILE_BEGIN);

		LoadedImage->MappedAddress = (PUCHAR)LocalAlloc(LPTR, MAP_IT_BUF_SIZE);
		if ( NULL == LoadedImage->MappedAddress )
		{
			__leave;
		}

		DosHeader = (PIMAGE_DOS_HEADER)LoadedImage->MappedAddress;
		if ( !::ReadFileBlock(hFile, DosHeader, sizeof(*DosHeader), cbLoaded) )
		{
			__leave;
		}

		if ((DosHeader->e_magic != IMAGE_DOS_SIGNATURE) &&
			(DosHeader->e_magic != IMAGE_NT_SIGNATURE)) {
				__leave;
		}

		if (DosHeader->e_magic == IMAGE_DOS_SIGNATURE) {
			if (DosHeader->e_lfanew == 0) {
				__leave;
			}

			LoadedImage->FileHeader = (PIMAGE_NT_HEADERS)((LPBYTE)DosHeader + DosHeader->e_lfanew);

			if (
				// If IMAGE_NT_HEADERS would extend past the end of file...
				(PBYTE)LoadedImage->FileHeader + sizeof(IMAGE_NT_HEADERS) >
				(PBYTE)LoadedImage->MappedAddress + LoadedImage->SizeOfImage ||

				// ..or if it would begin in, or before the IMAGE_DOS_HEADER...
				(PBYTE)LoadedImage->FileHeader <
				(PBYTE)LoadedImage->MappedAddress + sizeof(IMAGE_DOS_HEADER)  )
			{
				// ...then e_lfanew is not as expected.
				// (Several Win95 files are in this category.)
				__leave;
			}

			if ( !ReadFileBlock(hFile, (LPBYTE)DosHeader + sizeof(*DosHeader), DosHeader->e_lfanew - sizeof(*DosHeader), cbLoaded) )
			{
				__leave;
			}

		} else {

			// No DOS header indicates an image built w/o a dos stub

			LoadedImage->FileHeader = (PIMAGE_NT_HEADERS)DosHeader;
			SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
			cbLoaded = 0;
		}

		if ( !::ReadFileBlock(hFile, LoadedImage->FileHeader, offsetof(IMAGE_NT_HEADERS, OptionalHeader), cbLoaded) )
		{
			__leave;
		}
		if ( LoadedImage->FileHeader->Signature != IMAGE_NT_SIGNATURE ) {
			__leave;
		}

		// No optional header indicates an object...

		const WORD& cbOptionalHeader = LoadedImage->FileHeader->FileHeader.SizeOfOptionalHeader;
		if ( !cbOptionalHeader ) {
			__leave;
		}

		if ( !::ReadFileBlock(hFile, (LPBYTE)LoadedImage->FileHeader + offsetof(IMAGE_NT_HEADERS, OptionalHeader), cbOptionalHeader, cbLoaded) )
		{
			__leave;
		}

		// Check for versions < 2.50

		if ( LoadedImage->FileHeader->OptionalHeader.MajorLinkerVersion < 3 &&
			LoadedImage->FileHeader->OptionalHeader.MinorLinkerVersion < 5 ) {
				__leave;
		}

		InitializeListHead( &LoadedImage->Links );
		LoadedImage->NumberOfSections = LoadedImage->FileHeader->FileHeader.NumberOfSections;
		LoadedImage->Sections = IMAGE_FIRST_SECTION(LoadedImage->FileHeader);

		if ( !::ReadFileBlock(hFile, (LPBYTE)LoadedImage->Sections, sizeof(*LoadedImage->Sections) * LoadedImage->NumberOfSections, cbLoaded) )
		{
			__leave;
		}
		fRC = TRUE;

	} __except ( EXCEPTION_EXECUTE_HANDLER )
	{
	}
	if ( !fRC )
	{
		if ( NULL != LoadedImage->MappedAddress )
		{
			LocalFree(LoadedImage->MappedAddress);
			ZeroMemory(LoadedImage, sizeof(*LoadedImage));
		}
	}
	return fRC;
#if 0
    hMappedFile = CreateFileMapping(
                    hFile,
                    NULL,
                    PAGE_READONLY,
                    0,
                    0,
                    NULL
                    );
    if ( !hMappedFile ) {
        return FALSE;
    }

    LoadedImage->MappedAddress = (PUCHAR) MapViewOfFile(
                                    hMappedFile,
                                    FILE_MAP_READ,
                                    0,
                                    0,
                                    0
                                    );

    CloseHandle(hMappedFile);

    if (!LoadedImage->MappedAddress) {
        return (FALSE);
    }

    if (!CalculateImagePtrs(LoadedImage)) {
        UnmapViewOfFile(LoadedImage->MappedAddress);
        return(FALSE);
    }

    LoadedImage->hFile = INVALID_HANDLE_VALUE;

    return(TRUE);
#endif
}

typedef struct _EXCLUDE_RANGE {
    DWORD Offset;
    DWORD Size;
    struct _EXCLUDE_RANGE *Next;
} EXCLUDE_RANGE;

class EXCLUDE_LIST
{
    public:
        EXCLUDE_LIST() {
            m_Image = NULL;
            m_ExRange = new EXCLUDE_RANGE;

            if(m_ExRange)
                memset(m_ExRange, 0x00, sizeof(EXCLUDE_RANGE));
        }

        ~EXCLUDE_LIST() {
            EXCLUDE_RANGE *pTmp;
            pTmp = m_ExRange->Next;
            while (pTmp)
            {
                DELETE_OBJECT(m_ExRange);
                m_ExRange = pTmp;
                pTmp = m_ExRange->Next;
            }
            DELETE_OBJECT(m_ExRange);
        }

        void Init(LOADED_IMAGE * Image, DIGEST_FUNCTION pFunc, DIGEST_HANDLE dh) {
            m_Image = Image;
            m_ExRange->Offset = NULL;
            m_ExRange->Size = 0;
            m_pFunc = pFunc;
            m_dh = dh;
            return;
        }

		void Add(DWORD Offset, DWORD Size);

        BOOL Emit(PBYTE Offset, DWORD Size);
		BOOL Emit( HANDLE hFile, DWORD Size );
private:
	BOOL EmitFileSec(HANDLE hFile, DWORD EmitSize, LPBYTE buf, DWORD bufSize = MAP_IT_BUF_SIZE);

	LOADED_IMAGE  * m_Image;
	EXCLUDE_RANGE * m_ExRange;
	DIGEST_FUNCTION m_pFunc;
	DIGEST_HANDLE m_dh;
};

void
EXCLUDE_LIST::Add(
    DWORD Offset,
    DWORD Size
    )
{
    EXCLUDE_RANGE *pTmp, *pExRange;

    pExRange = m_ExRange;

    while (pExRange->Next && (pExRange->Next->Offset < Offset)) {
        pExRange = pExRange->Next;
    }

    pTmp = new EXCLUDE_RANGE;

    if(pTmp)
    {
        pTmp->Next = pExRange->Next;
        pTmp->Offset = Offset;
        pTmp->Size = Size;
        pExRange->Next = pTmp;
    }

    return;
}

#if 0
BOOL
EXCLUDE_LIST::Emit(
    PBYTE Offset,
    DWORD Size
    )
{
    BOOL rc;

    EXCLUDE_RANGE *pExRange;
    DWORD EmitSize, ExcludeSize;

    pExRange = m_ExRange->Next;

    while (pExRange && (Size > 0)) {
        if (pExRange->Offset >= Offset) {
            // Emit what's before the exclude list.
            EmitSize = min((DWORD)(pExRange->Offset - Offset), Size);
            if (EmitSize) {
                rc = (*m_pFunc)(m_dh, Offset, EmitSize);
                Size -= EmitSize;
                Offset += EmitSize;
            }
        }

        if (Size) {
            if (pExRange->Offset + pExRange->Size >= Offset) {
                // Skip over what's in the exclude list.
                ExcludeSize = min(Size, (DWORD)(pExRange->Offset + pExRange->Size - Offset));
                Size -= ExcludeSize;
                Offset += ExcludeSize;
            }
        }

        pExRange = pExRange->Next;
    }

    // Emit what's left.
    if (Size) {
        rc = (*m_pFunc)(m_dh, Offset, Size);
    }
    return rc;
}
#endif

BOOL EXCLUDE_LIST::EmitFileSec(HANDLE hFile, DWORD EmitSize, LPBYTE buf, DWORD bufSize /*= MAP_IT_BUF_SIZE*/)
{
	DWORD dwEmitted = 0;
	BOOL rc = FALSE;

	DWORD Remaining = EmitSize;
	for ( ; Remaining >= MAP_IT_BUF_SIZE; Remaining -= bufSize )
	{
		if ( ReadFileBlock(hFile, buf, bufSize, dwEmitted, TRUE, bufSize) )
		{
			rc = (*m_pFunc)(m_dh, buf, bufSize);
		}
		else
		{
			return FALSE;
		}
	}
	if ( Remaining )
	{
		if ( ReadFileBlock(hFile, buf, Remaining, dwEmitted, TRUE, bufSize) )
		{
			rc = (*m_pFunc)(m_dh, buf, Remaining);
		}
		else
		{
			return FALSE;
		}
	}
	return rc;
}

BOOL
EXCLUDE_LIST::Emit(
				   HANDLE hFile,
				   DWORD Size
				   )
{
	BOOL rc = FALSE;
	LPBYTE buf = (LPBYTE)LocalAlloc(LPTR, MAP_IT_BUF_SIZE);
	if ( NULL == buf )
	{
		return rc;
	}

	EXCLUDE_RANGE *pExRange = NULL;
	DWORD EmitSize = 0, ExcludeSize = 0, Emitted = 0;

	pExRange = m_ExRange->Next;

	SetFilePointer(hFile, 0, NULL, FILE_BEGIN);
	while (pExRange && (Size > 0)) {
		if (pExRange->Offset >= Emitted) {
			// Emit what's before the exclude list.
			EmitSize = min((DWORD)(pExRange->Offset - Emitted), Size);
			if (EmitSize) {
				rc = EmitFileSec(hFile, EmitSize, buf);
				//rc = (*m_pFunc)(m_dh, Offset, EmitSize);
				Size -= EmitSize;
				Emitted += EmitSize;
			}
		}

		if (Size) {
			if (pExRange->Offset + pExRange->Size >= Emitted) {
				// Skip over what's in the exclude list.
				ExcludeSize = min(Size, (DWORD)(pExRange->Offset + pExRange->Size - Emitted));
				SetFilePointer(hFile, ExcludeSize, NULL, FILE_CURRENT);
				Size -= ExcludeSize;
				Emitted += ExcludeSize;
			}
		}

		pExRange = pExRange->Next;
	}

	// Emit what's left.
	if (Size) {
		//rc = (*m_pFunc)(m_dh, Offset, Size);
		rc = EmitFileSec(hFile, Size, buf);
	}
	::LocalFree(buf);
	return rc;
}

BOOL
imagehack_IsImagePEOnly(
    IN HANDLE           FileHandle
    )
/*
   What we're looking for here is if there's data outside the exe.
   To do so, find the highest section header offset.  To that, find the
   highest debug directory offset.  Finally, round up to the file alignment
   size, add in the cert size, and compare to the reported image size...
*/
{

    LOADED_IMAGE    LoadedImage;
    DWORD HighOffset;
    DWORD i, Offset, Size;
    LONG DebugDirectorySize = 0, CertSize = 0;
    PIMAGE_DEBUG_DIRECTORY DebugDirectory = NULL;

    BOOL rc;
    DWORD FileAlignment;
    DWORD NumberOfSections;

    if (MapIt(FileHandle, &LoadedImage) == FALSE) {
        return(FALSE);
    }

    rc = FALSE;

    __try {
        if (LoadedImage.FileHeader->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR32_MAGIC) {
            FileAlignment = ((PIMAGE_NT_HEADERS32)LoadedImage.FileHeader)->OptionalHeader.FileAlignment;
        } else if (LoadedImage.FileHeader->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR64_MAGIC) {
            FileAlignment = ((PIMAGE_NT_HEADERS64)LoadedImage.FileHeader)->OptionalHeader.FileAlignment;
        } else {
            __leave;
        }

        NumberOfSections =  LoadedImage.FileHeader->FileHeader.NumberOfSections;
        HighOffset = 0;

        for (i = 0; i < NumberOfSections; i++) {
            Offset = LoadedImage.Sections[i].PointerToRawData;
            Size = LoadedImage.Sections[i].SizeOfRawData;
            HighOffset = max(HighOffset, (Offset + Size));
        }

		DWORD_PTR DebugDirOffSet = mssip_ImageDirectoryEntryToData(
			LoadedImage,
			IMAGE_DIRECTORY_ENTRY_DEBUG,
			(ULONG *) &DebugDirectorySize
			);
		DebugDirectory = (PIMAGE_DEBUG_DIRECTORY)::LocalAlloc(LPTR, DebugDirectorySize);
		if ( NULL == DebugDirectory )
		{
			__leave;
		}
		
		if ( 0xFFFFFFFF == SetFilePointer(FileHandle, DebugDirOffSet, NULL, FILE_BEGIN) )
		{
			__leave;
		}

		DWORD cbLoaded = 0;
		if ( !ReadFileBlock(FileHandle, DebugDirectory, DebugDirectorySize, cbLoaded, TRUE, DebugDirectorySize) )
		{
			__leave;
		}

		PIMAGE_DEBUG_DIRECTORY DebugDirectoryIterator = DebugDirectory;
        while (DebugDirectorySize > 0) {
            Offset = DebugDirectoryIterator->PointerToRawData;
            Size = DebugDirectoryIterator->SizeOfData;
            HighOffset = max(HighOffset, (Offset + Size));
            DebugDirectorySize -= sizeof(IMAGE_DEBUG_DIRECTORY);
            DebugDirectoryIterator++;
        }

        HighOffset = AlignIt(HighOffset, FileAlignment);

        DWORD_PTR CertDir = mssip_ImageDirectoryEntryToData(
                            LoadedImage,
                            IMAGE_DIRECTORY_ENTRY_SECURITY,
                            (ULONG *) &CertSize
                          );

        if (LoadedImage.SizeOfImage <= (HighOffset + CertSize)) {
            rc = TRUE;
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) { }

    if ( NULL != LoadedImage.MappedAddress )
	{
		::LocalFree(LoadedImage.MappedAddress);
	}
	if ( NULL != DebugDirectory )
	{
		::LocalFree(DebugDirectory);
	}

    return(rc);
}

BOOL
imagehack_AuImageGetDigestStream(
    IN HANDLE           FileHandle,
    IN DWORD            DigestLevel,
    IN DIGEST_FUNCTION  DigestFunction,
    IN DIGEST_HANDLE    DigestHandle
    )

/*++

Routine Description:
    Given an image, return the bytes necessary to construct a certificate.
    Only PE images are supported at this time.

Arguments:

    FileHandle  -   Handle to the file in question.  The file should be opened
                    with at least GENERIC_READ access.

    DigestLevel -   Indicates what data will be included in the returned buffer.
                    Valid values are:

                        CERT_PE_IMAGE_DIGEST_ALL_BUT_CERTS - Include data outside the PE image itself
                                                              (may include non-mapped debug symbolic)

    DigestFunction - User supplied routine that will process the data.

    DigestHandle -  User supplied handle to identify the digest.  Passed as the first
                    argument to the DigestFunction.

Return Value:

    TRUE         - Success.

    FALSE        - There was some error.  Call GetLastError for more information.  Possible
                   values are ERROR_INVALID_PARAMETER or ERROR_OPERATION_ABORTED.

--*/

{
	imagehack_IsImagePEOnly(FileHandle);

	DWORD imagehack_AuImageGetDigestStream_(
		IN HANDLE           FileHandle,
		IN DWORD            DigestLevel,
		IN DIGEST_FUNCTION  DigestFunction,
		IN DIGEST_HANDLE    DigestHandle,
		IN OUT EXCLUDE_LIST& ExList);
	EXCLUDE_LIST    ExList;
	BOOL            rc = imagehack_AuImageGetDigestStream_(FileHandle, DigestLevel, DigestFunction, DigestHandle, ExList);

    SetLastError(rc);

    return(rc == ERROR_SUCCESS ? TRUE : FALSE);
}

DWORD imagehack_AuImageGetDigestStream_(
										IN HANDLE           FileHandle,
										IN DWORD            DigestLevel,
										IN DIGEST_FUNCTION  DigestFunction,
										IN DIGEST_HANDLE    DigestHandle,
										IN OUT EXCLUDE_LIST& ExList)
{

	LOADED_IMAGE    LoadedImage;
	BOOL            rc;

	if (MapIt(FileHandle, &LoadedImage) == FALSE) {
		// Unable to map the image or invalid digest level.
		SetLastError(ERROR_INVALID_PARAMETER);
		return(FALSE);
	}

	rc = ERROR_INVALID_PARAMETER;
	__try {

		if ((LoadedImage.FileHeader->OptionalHeader.Magic != IMAGE_NT_OPTIONAL_HDR32_MAGIC) &&
			(LoadedImage.FileHeader->OptionalHeader.Magic != IMAGE_NT_OPTIONAL_HDR64_MAGIC))
		{
			__leave;
		}

		ExList.Init(&LoadedImage, DigestFunction, DigestHandle);

		if (LoadedImage.FileHeader->OptionalHeader.Magic == IMAGE_NT_OPTIONAL_HDR32_MAGIC) {
			PIMAGE_NT_HEADERS32 NtHeader32 = (PIMAGE_NT_HEADERS32)(LoadedImage.FileHeader);
			// Exclude the checksum.
			//ExList.Add(((DWORD_PTR) &NtHeader32->OptionalHeader.CheckSum),
			//	sizeof(NtHeader32->OptionalHeader.CheckSum));
			ExList.Add(((LPBYTE) &NtHeader32->OptionalHeader.CheckSum - LoadedImage.MappedAddress),
				sizeof(NtHeader32->OptionalHeader.CheckSum));

			// Exclude the Security directory.
			//ExList.Add(((DWORD_PTR) &NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]),
			//	sizeof(NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]));
			ExList.Add(((LPBYTE) &NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY] - LoadedImage.MappedAddress ),
				sizeof(NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]));

			// Exclude the certs.
			//ExList.Add((DWORD_PTR)NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].VirtualAddress +
			//	(DWORD_PTR)LoadedImage.MappedAddress,
			//	NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].Size);
			//DWORD OffSet = (DWORD)RtlImageRvaToVa(NtHeader32, NULL, NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].VirtualAddress, NULL);
			DWORD OffSet = NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].VirtualAddress;
			ExList.Add(OffSet,
				NtHeader32->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].Size);
		} else {
			PIMAGE_NT_HEADERS64 NtHeader64 = (PIMAGE_NT_HEADERS64)(LoadedImage.FileHeader);
			// Exclude the checksum.
			//ExList.Add(((DWORD_PTR) &NtHeader64->OptionalHeader.CheckSum),
			//	sizeof(NtHeader64->OptionalHeader.CheckSum));
			ExList.Add(((LPBYTE) &NtHeader64->OptionalHeader.CheckSum - LoadedImage.MappedAddress),
				sizeof(NtHeader64->OptionalHeader.CheckSum));

			// Exclude the Security directory.
			//ExList.Add(((DWORD_PTR) &NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]),
			//	sizeof(NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]));
			ExList.Add(((LPBYTE) &NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY] - LoadedImage.MappedAddress),
				sizeof(NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]));

			// Exclude the certs.
			//ExList.Add((DWORD_PTR)NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].VirtualAddress +
			//	(DWORD_PTR)LoadedImage.MappedAddress,
			//	NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].Size);
			ExList.Add((DWORD_PTR)NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].VirtualAddress,
				NtHeader64->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY].Size);
		}

		//ExList.Emit((PBYTE) (LoadedImage.MappedAddress), LoadedImage.SizeOfImage);
		ExList.Emit(FileHandle, LoadedImage.SizeOfImage);
		rc = ERROR_SUCCESS;

	} __except(EXCEPTION_EXECUTE_HANDLER) { }

	if ( NULL != LoadedImage.MappedAddress )
	{
		LocalFree(LoadedImage.MappedAddress);
	}
	return rc;
}

BOOL mssip_ImageGetCertificateData(
								   __in HANDLE FileHandle,
								   __in DWORD CertificateIndex,
								   __out LPWIN_CERTIFICATE Certificate,
								   __inout PDWORD RequiredLength
								   )
{
	BOOL bRet = FALSE;
	LOADED_IMAGE Image;
	ZeroMemory(&Image, sizeof(Image));

	__try
	{
		if ( NULL == RequiredLength )
		{
			SetLastError(ERROR_INVALID_PARAMETER);
			__leave;
		}

		*RequiredLength = 0;

		if ( !MapIt(FileHandle, &Image) )
		{
			__leave;
		}

		PIMAGE_DATA_DIRECTORY Dir = NULL;
		WORD wOptionalHeaderMagic = Image.FileHeader->OptionalHeader.Magic;
		if ( IMAGE_NT_OPTIONAL_HDR32_MAGIC == wOptionalHeaderMagic )
		{
			Dir = &(((IMAGE_OPTIONAL_HEADER32&)Image.FileHeader->OptionalHeader).DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]);
		}
		else if ( IMAGE_NT_OPTIONAL_HDR64_MAGIC == wOptionalHeaderMagic )
		{
			Dir = &(((IMAGE_OPTIONAL_HEADER64&)Image.FileHeader->OptionalHeader).DataDirectory[IMAGE_DIRECTORY_ENTRY_SECURITY]);
		}
		else
		{
			__leave;
		}

		DWORD Len = Dir->Size;
		DWORD Offset = Dir->VirtualAddress;
		if ( 0 == Len || 0 == Offset )
		{
			__leave;
		}
		if ( NULL == Certificate )
		{
			*RequiredLength = Len;
			bRet = TRUE;
			__leave;
		}

		SetFilePointer(FileHandle, Offset, NULL, FILE_BEGIN);
		bRet = ::ReadFile(FileHandle, Certificate, Len, RequiredLength, NULL);
	} __except ( EXCEPTION_EXECUTE_HANDLER ) { }

	if ( NULL != Image.MappedAddress )
	{
		::LocalFree(Image.MappedAddress);
	}

	return bRet;
}

DWORD_PTR mssip_ImageDirectoryEntryToData(
									  LOADED_IMAGE& Image,
									  __in USHORT DirectoryEntry,
									  __out PULONG Size
									  )
{
	DWORD_PTR Offset = 0;
	__try
	{
		if ( DirectoryEntry >= IMAGE_NUMBEROF_DIRECTORY_ENTRIES )
		{
			__leave;
		}

		PIMAGE_DATA_DIRECTORY Dir = NULL;
		WORD wOptionalHeaderMagic = Image.FileHeader->OptionalHeader.Magic;
		if ( IMAGE_NT_OPTIONAL_HDR32_MAGIC == wOptionalHeaderMagic )
		{
			Dir = &(((IMAGE_OPTIONAL_HEADER32&)(Image.FileHeader->OptionalHeader)).DataDirectory[DirectoryEntry]);
		}
		else if ( IMAGE_NT_OPTIONAL_HDR64_MAGIC == wOptionalHeaderMagic )
		{
			Dir = &(((IMAGE_OPTIONAL_HEADER64&)(Image.FileHeader->OptionalHeader)).DataDirectory[DirectoryEntry]);
		}
		else
		{
			__leave;
		}
		*Size = Dir->Size;
		Offset = (DWORD_PTR)RtlImageRvaToVa(Image.FileHeader, NULL, Dir->VirtualAddress, NULL);

	} __except ( EXCEPTION_EXECUTE_HANDLER ) { }

	return Offset;
}

PIMAGE_SECTION_HEADER WINAPI RtlImageRvaToSection( const IMAGE_NT_HEADERS *nt, 
												  HMODULE module, DWORD rva ) 
{ 
     int i; 
     const IMAGE_SECTION_HEADER *sec; 
  
     sec = (const IMAGE_SECTION_HEADER*)((const char*)&nt->OptionalHeader + 
                                         nt->FileHeader.SizeOfOptionalHeader); 
     for (i = 0; i < nt->FileHeader.NumberOfSections; i++, sec++) 
     { 
         if ((sec->VirtualAddress <= rva) && (sec->VirtualAddress + sec->SizeOfRawData > rva)) 
             return (PIMAGE_SECTION_HEADER)sec; 
     } 
     return NULL; 
}

 PVOID WINAPI RtlImageRvaToVa( const IMAGE_NT_HEADERS *nt, HMODULE module, DWORD rva, IMAGE_SECTION_HEADER **section ) 
 { 
	 IMAGE_SECTION_HEADER *sec; 
	 
	 if (section && *section)  /* try this section first */ 
	 { 
		 sec = *section; 
		 if ((sec->VirtualAddress <= rva) && (sec->VirtualAddress + sec->SizeOfRawData > rva)) 
			 goto found; 
	 } 
	 if (!(sec = RtlImageRvaToSection( nt, module, rva ))) return NULL; 

found: 
	 if (section) *section = sec; 
	 return (char *)module + sec->PointerToRawData + (rva - sec->VirtualAddress); 
 }
