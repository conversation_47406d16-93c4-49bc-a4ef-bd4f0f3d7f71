#include "md5helper.h"
#include "md5.h"

namespace md5helper
{
	static std::string Bin2HString(const unsigned char* pbySrc, unsigned long ulSize)
	{
		char dst[65] = { 0 };

		unsigned char byTemp;
		unsigned long i = 0;
		for (i = 0; i < ulSize; i++)
		{
			byTemp = pbySrc[i] & 0x0F;
			if (byTemp < 0x0A)
				dst[(i << 1) + 1] = byTemp + 0x30;
			else
				dst[(i << 1) + 1] = byTemp + 0x37;

			byTemp = ((pbySrc[i] >> 4) & 0x0F);
			if (byTemp < 0x0A)
				dst[i << 1] = byTemp + 0x30;
			else
				dst[i << 1] = byTemp + 0x37;
		}
		dst[i << 1] = '\0';

		return std::string(dst);
	}

	std::string CalcStringMD5(const std::string& str)
	{
		if (!str.size())
			return "";

		md5_byte_t md5_buf[16];
		md5_state_s mst;
		md5_init(&mst);
		md5_append(&mst, (const md5_byte_t*)str.c_str(), (int)str.size());
		md5_finish(&mst, md5_buf);

		std::string md5 = Bin2HString(md5_buf, 16);
		return md5;
	}
};