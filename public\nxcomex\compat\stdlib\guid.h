#ifndef _COMPAT_GUID_H_
#define _COMPAT_GUID_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" 
{
#endif

#define GUIDFormatString	"{%08X-%04X-%04X-%02X%02X-%02X%02X%02X%02X%02X%02X}"
#define GUIDFormatWString	L"{%08X-%04X-%04X-%02X%02X-%02X%02X%02X%02X%02X%02X}"
#define GUIDStringLength    40
#define GUIDClassName		"0x%08X, 0x%04X, 0x%04X, 0x%02X, 0x%02X, 0x%02X, 0x%02X, 0x%02X, 0x%02X, 0x%02X, 0x%02X"

GUID StringToGUID(const char* lpString);
GUID WStringToGUID(wchar_t* lpString);
const char* GUIDToString(const GUID* guid, char* lpGuidBuf);
const wchar_t* GUIDToWString(const GUID* guid, wchar_t* lpGuidBuf);

const char* StringToGUIDClassName(const char* strguid, char* lpGuidBuf);

#ifdef	__cplusplus
}
# endif

#endif

