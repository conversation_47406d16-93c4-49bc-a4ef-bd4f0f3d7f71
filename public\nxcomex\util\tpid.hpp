#ifndef _UTIL_TPID_HPP_
#define _UTIL_TPID_HPP_

#include <util/core.hpp>

#define OS_THREAD_BUSY	100
#define OS_THREAD_ERROR 1
#define OS_THREAD_SUC	0

#if (TARGET_OS == OS_WINDOWS)
    typedef int                 _pid_t;
    typedef HANDLE              _thread_t;
	typedef DWORD				_thread_id;
	typedef HANDLE              _pipe_t;
	#include <util/win/sysvar.hpp>
#elif (TARGET_OS == OS_POSIX)
    typedef pid_t               _pid_t;
    typedef pthread_t           _thread_t;
	typedef DWORD				_thread_id;
	typedef int              	_pipe_t;
	#define INVALID_HANDLE_VALUE     0 
	#include <pthread.h>
	#include <util/posix/sysvar.hpp>
#elif (TARGET_OS == OS_UNIX)
    typedef pid_t               _pid_t;
    typedef pthread_t           _thread_t;
	typedef DWORD				_thread_id;
	typedef int              	_pipe_t;
	#define INVALID_HANDLE_VALUE     0 
	#include <pthread.h>
	#include <util/unix/sysvar.hpp>
#endif

#endif 
