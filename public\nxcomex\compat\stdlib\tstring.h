#ifndef _COMPAT_TSTRING_H_
#define _COMPAT_TSTRING_H_

#include <compat/stdlib/str.h>
#include <compat/stdlib/wstr.h>
#include <compat/stdlib/memory.h>

#define string_strcpy(x,n,y)			s_strcpy(x,n,y)
#define string_strcat(x,n,y)			s_strcat(x,n,y)
#define string_strlen(x)				s_strlen(x)

#define wstring_strcpy(x,n,y)			s_wstrcpy(x,n,y)
#define wstring_strcat(x,n,y)			s_wstrcat(x,n,y)
#define wstring_strchr(x,y)				s_wstrchr(x,y)
#define wstring_strlen(x)				s_wstrlen(x)

#ifndef bzero
#define bzero(x,y)						s_memset(x,0,y)
#endif

#if (TARGET_OS == OS_WINDOWS)
	#define wstring_stricmp(x,y)	    s_wstricmp(x,y)
	#define string_stricmp(x,y)		    s_stricmp(x,y)
	#define tstring_stricmp(x,y)		s_wstricmp(x,y)
	#define tstring_strcpy(x,n,y)	    s_wstrcpy(x,n,y)
	#define tstring_strcat(x,n,y)	    s_wstrcat(x,n,y)
	#define tstring_strchr(x,y)		    s_wstrchr(x,y)
	#define tstring_strlen(x)			s_wstrlen(x)
	#define tstring_ststr(x,y)			s_wstrstr(x,y)
	#define tmemset(x,y,z)				s_wmemset(x,y,z)
#elif (TARGET_OS == OS_POSIX)
	#define wstring_stricmp(x,y)	    s_wstrcasecmp(x,y)
	#define string_stricmp(x,y)		    s_strcasecmp(x,y)
	#define tstring_stricmp(x,y)		s_strcasecmp(x,y)
	#define tstring_strcpy(x,n,y)	    s_strcpy(x,n,y)
	#define tstring_strcat(x,n,y)	    s_strcat(x,n,y)
	#define tstring_strchr(x,y)		    s_strchr(x,y)
	#define tstring_strlen(x)		    s_strlen(x)
	#define tstring_ststr(x,y)			s_strstr(x,y)
	#define tmemset(x,y,z)				s_memset(x,y,z)
#elif (TARGET_OS == OS_DARWIN)
	#define wstring_stricmp(x,y)	    s_wstrcasecmp(x,y)
	#define string_stricmp(x,y)		    s_strcasecmp(x,y)
	#define tstring_stricmp(x,y)		s_strcasecmp(x,y)
	#define tstring_strcpy(x,n,y)	    s_strcpy(x,n,y)
	#define tstring_strcat(x,n,y)	    s_strcat(x,n,y)
	#define tstring_strchr(x,y)		    s_strchr(x,y)
	#define tstring_strlen(x)		    s_strlen(x)
	#define tstring_ststr(x,y)			s_strstr(x,y)
	#define tmemset(x,y,z)				s_memset(x,y,z)
#endif





#endif
