#pragma once
#ifndef _ITAIL_H
#define _ITAIL_H

#include <dlcom/unknown.h>

uvStdComNameSpaceBegin

interface IPluginExit : public IBase
{
	std_method(Exit)() PURE;
};

// {213C730E-8B0C-4B86-BC61-E1A66AF71CD8}
_DEFINE_IID(IID_IPluginExit,
		0x213c730e, 0x8b0c, 0x4b86, 0xbc, 0x61, 0xe1, 0xa6, 0x6a, 0xf7, 0x1c, 0xd8 );

// {39EDA2FA-55AC-457E-9452-E7D2182F8950}
_DEFINE_GUID_IMPL(CLSID_PluginTail,
	0x39eda2fa, 0x55ac, 0x457e, 0x94, 0x52, 0xe7, 0xd2, 0x18, 0x2f, 0x89, 0x50);

uvStdComNameSpaceEnd

#endif
