#ifndef _COMPAT_LIST_
#define _COMPAT_LIST_

#include <compat/define.h>

#ifdef  __cplusplus
extern "C"
{
#endif

/*

//struct Node {
//	_lComPtr<ITcpSocket> pSocket;
//	struct _list_item list;
//};



//_list_init(&m_queue);
//==============================
//_list_item_init(&pNode->list);
//_list_insert(&m_queue, &pNode->list, _list_begin(&m_queue));
//===============================
//struct _list_item *it = _list_begin(&m_queue);
//while (it != _list_end(&m_queue)) {

//	struct Node *item = NULL;
//	item = _cont_of(it, struct Node, list);
//	rc_assert_break(item != NULL)
//	item->pSocket->CloseIo(CLOSE_BOTH_IO);
//	item->pSocket->CloseSock();
//	item->pSocket->Release();
//	it = _list_erase(&m_queue, &item->list);
//	heap_free(item);
//}

struct nn_timerset_hndl {
	struct _list_item list;
	uint64_t timeout;
};


int main()
{

	struct _list_s timeouts;
	struct _list_item *it;
	struct nn_timerset_hndl hndl;
	struct nn_timerset_hndl hndl1;
	struct nn_timerset_hndl *ith;
	_list_init(&timeouts);

	_list_item_init(&hndl.list);
	_list_item_init(&hndl1.list);

	hndl.timeout = 10;
	hndl1.timeout = 101;

	_list_insert(&timeouts, &hndl.list, _list_end(&timeouts));
	_list_insert(&timeouts, &hndl1.list, _list_end(&timeouts));


	for (it = _list_begin(&timeouts);
		it != _list_end(&timeouts);
		it = _list_next(&timeouts, it)) {
		ith = _cont_of(it, struct nn_timerset_hndl, list);

		uint64_t i;
		i = ith->timeout;
		printf("%llu\n", i);
	}
	_list_erase(&timeouts, &hndl1.list);


	printf("aa");

	for (it = _list_begin(&timeouts);
		it != _list_end(&timeouts);
		it = _list_next(&timeouts, it)) {
		ith = _cont_of(it, struct nn_timerset_hndl, list);

		uint64_t i;
		i = ith->timeout;
		printf("%llu\n", i);
	}
	_list_term(&timeouts);

	return 0;
}

*/

struct _list_item {
    struct _list_item *next;
    struct _list_item *prev;
};


struct _list_s {
    struct _list_item *first;
    struct _list_item *last;
};

#define _LIST_NOTINLIST ((struct _list_item*) -1)

#define _LIST_ITEM_INITIALIZER {_LIST_NOTINLIST, _LIST_NOTINLIST}


void _list_init (struct _list_s *self);
void _list_term (struct _list_s *self);
int _list_empty (struct _list_s *self);
struct _list_item *_list_begin (struct _list_s *self);
struct _list_item *_list_end (struct _list_s *self);

struct _list_item *_list_prev (struct _list_s *self,
    struct _list_item *it);
struct _list_item *_list_next (struct _list_s *self,
    struct _list_item *it);

void _list_insert (struct _list_s *self, struct _list_item *item,
    struct _list_item *it);

struct _list_item *_list_erase (struct _list_s *self,
    struct _list_item *item);

void _list_item_init (struct _list_item *self);
void _list_item_term (struct _list_item *self);
int _list_item_isinlist (struct _list_item *self);

#ifdef  __cplusplus
}
#endif

#endif

