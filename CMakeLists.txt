cmake_minimum_required (VERSION 3.10)
if("${AT_BUILD_PLATFORM}" STREQUAL "arm64")
	SET(CUSTOM_PLATFORM "arm64")
	SET(CMAKE_C_COMPILER aarch64-7_26-linux-gnu-gcc)
	SET(CMAKE_CXX_COMPILER aarch64-7_26-linux-gnu-g++)
elseif("${AT_BUILD_PLATFORM}" STREQUAL "linux")
	SET(CMAKE_C_COMPILER x86_64-7-linux-gnu-gcc)
	SET(CMAKE_CXX_COMPILER x86_64-7-linux-gnu-g++)
elseif("${AT_BUILD_PLATFORM}" STREQUAL "windows")
	SET(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
	SET(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
	SET(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
if(NOT DEFINED CMAKE_TOOLCHAIN_FILE)
	# SET(CMAKE_TOOLCHAIN_FILE "C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake")
	# SET(VCPKG_TARGET_TRIPLET x64-windows-static)
	# SET(CMAKE_GENERATOR_PLATFORM x64)
	# SET(CMAKE_SUPPRESS_DEVELOPER_WARNINGS TRUE)
endif()
endif()
project(usbprotector C CXX)

SET(COMMON_SOURCE
	usbprotector.cpp
	utility.cpp
	global_defs.cpp
	policy_parser.cpp
	public/utilset/agent_cfg_parser.cpp
	public/utilset/driver_utils.cpp
	public/utilset/network_helper.cpp
	public/utilset/uuid4.c
)

######################################################################
####build linux
######################################################################
if(AT_BUILD_PLATFORM STREQUAL "linux" OR AT_BUILD_PLATFORM STREQUAL "arm64")
	message(STATUS "This is a Linux platform.")
	
	add_definitions(-D__LINUX__)
	ADD_DEFINITIONS(-D_USE_NXCOM -D_USE_QLOG -D_STD_COMM -D_GNU_SOURCE)
	#添加编译选项
	# add_compile_options(-O2)
	#添加预处理器定义 -O2
	# ADD_DEFINITIONS(-O2)
	#C++11标准
	SET(CMAKE_CXX_FLAGS "-Os -s -std=c++11 ${CMAKE_CXX_FLAGS}")
	set(Sources
		"interface.cpp"
		"PluginRegister.cpp"
		"dllmain.cpp"
		linux/usb_hotplug_monitor.cpp
		linux/usb_device_manager.cpp
	)
	source_group("Sources" FILES ${Sources})
	set(ALL_FILES
		${Sources}
		${COMMON_SOURCE}
	)
	SET(CMAKE_CXX_FLAGS "-static-libstdc++ -static-libgcc -Wl,--exclude-libs,ALL ${CMAKE_CXX_FLAGS}")
	add_library(${PROJECT_NAME} SHARED ${ALL_FILES})
	target_include_directories(${PROJECT_NAME} PUBLIC
		"${CMAKE_CURRENT_SOURCE_DIR}/public/nxcomex;"
		"${CMAKE_CURRENT_SOURCE_DIR}/public"
		"${CMAKE_CURRENT_SOURCE_DIR}/nlohmann"
		"${CMAKE_CURRENT_SOURCE_DIR}/public/acsmon"
		third_party/libudev-zero-1.0.3/include
		linux
		${CMAKE_CURRENT_SOURCE_DIR}
	)
	if("${AT_BUILD_PLATFORM}" STREQUAL "arm64")
		set(ADDITIONAL_LIBRARY_DEPENDENCIES
			"-lbase_arm64"
			"-lpthread"
			"-lqlog_arm64"
			"-ldl"
			"-ludev"
			${_REFLECTION}
			${_GRPC_GRPCPP}
			${_PROTOBUF_LIBPROTOBUF}
		)
	else()
		set(ADDITIONAL_LIBRARY_DEPENDENCIES
			"-lbase"
			"-lpthread"
			"-lqlog"
			"-ldl"
			"-ludev"
			${_REFLECTION}
			${_GRPC_GRPCPP}
			${_PROTOBUF_LIBPROTOBUF}
		)
	endif()
	
	set(LINK_DIR
		${CMAKE_CURRENT_SOURCE_DIR}/public/lib
		third_party/libudev-zero-1.0.3/lib
	)
	target_link_directories(${PROJECT_NAME} PUBLIC ${LINK_DIR})
	target_link_libraries(${PROJECT_NAME} ${ADDITIONAL_LIBRARY_DEPENDENCIES})
	
	# add_custom_target(clean_file
	# 	COMMAND make clean -C ${CMAKE_CURRENT_BINARY_DIR}
	# 	COMMENT "Cleaning the build directory"
	# )
	# add_dependencies(${PROJECT_NAME} clean_file)

	# 获取 RELEASE,VERSION,BUILD
	execute_process(COMMAND git show -s --pretty=format:%h OUTPUT_VARIABLE BUILD)
	execute_process(COMMAND  git rev-parse --abbrev-ref HEAD OUTPUT_VARIABLE RELEASE_HEAD_OUT )

	if(NOT RELEASE_HEAD_OUT STREQUAL "")
		string(STRIP ${RELEASE_HEAD_OUT} RELEASE_HEAD)
	endif()
	string(STRIP ${RELEASE_HEAD}-${BUILD} RELEASE)
	string(TIMESTAMP VERSION "2.0.%y%m%d.%H%M")
	message("BUILD=${BUILD} RELEASE=${RELEASE} VERSION=${VERSION}")
	
	if("${AT_BUILD_PLATFORM}" STREQUAL "arm64")
        set(control_path ${CMAKE_CURRENT_SOURCE_DIR}/template/control.arm64)
    else()
        set(control_path ${CMAKE_CURRENT_SOURCE_DIR}/template/control.linux)
    endif()
	
	add_custom_target(
		make_usbprotector ALL
		echo "make usbprotector linux .so"
		COMMAND echo "copy over"
		echo "make usbprotector ap package"
		COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/pkg
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/ACSP
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/files
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/libusbprotector.so ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/usbprotector/usbprotector.atcom ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/usbprotector/usbprotector.atmon ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${control_path} ${CMAKE_CURRENT_BINARY_DIR}/pkg/ACSP/control
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/template/service.linux ${CMAKE_CURRENT_BINARY_DIR}/pkg/ACSP/service
		#打包
		COMMAND acspacker ap --path=${CMAKE_CURRENT_BINARY_DIR}/pkg --out=${CMAKE_CURRENT_BINARY_DIR}/usbprotector.ap --ver=${VERSION} --git=${RELEASE}
	)

	add_dependencies(make_usbprotector ${PROJECT_NAME})
endif()
if(AT_BUILD_PLATFORM STREQUAL "windows")
	######################################################################
	####build windows
	######################################################################
	
	add_definitions(-DWINDOWS_COMPILE)

	add_definitions(-D_WINDOWS -D_USRDLL -D_USE_NXCOM -D_USE_QLOG)
	set(CMAKE_CXX_STANDARD 11)
	set(CMAKE_CXX_STANDARD_REQUIRED True)
	
	#0：禁用所有迭代器调试功能。
	#这是默认设置，用于发布版本（Release），此时迭代器不包含额外的调试检查，以提高性能。
	# 1：启用基本的迭代器调试功能
	# 2：启用完整的迭代器调试功能 调试版本（Debug
	# 设置运行时库配置
	add_definitions(-D_ITERATOR_DEBUG_LEVEL=0)
	
	aux_source_directory(devcon DEVCON_SOURCE)

	if (CMAKE_HOST_WIN32) 
		set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /MT")
		set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /MTd")
		set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ./Lib/Debug)
		set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ./Lib/Debug)
		set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ./Bin/Debug)
		set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ./Lib/Release)
		set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ./Lib/Release)
		set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ./Bin/Release)
	
	add_compile_options(/W0)
	else()
	MESSAGE(FATAL_ERROR "Error host is not windows")
	endif()
	
	set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
	set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /OPT:REF /OPT:ICF")
	set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /OPT:REF /OPT:ICF") 
	#set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
	#set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF") 
	
	
	include_directories(${CMAKE_CURRENT_SOURCE_DIR})
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/nlohmann)
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/public/log4cplus1x)
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/public) 
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/public/nxcom) 
	include_directories(${CMAKE_CURRENT_SOURCE_DIR}/public/acsmon) 
	
	
	link_libraries(Ws2_32.lib qlog_x64_mt.lib setupapi.lib)
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/public/lib) 
	link_directories(${CMAKE_CURRENT_SOURCE_DIR}/public/acsmon) 
	# 设置编译器标志
	if (MSVC)
		# 添加编译器标志来忽略特定警告
		add_definitions(/w)
	else()
		# 如果使用其他编译器如 MinGW，可以添加相应的编译器标志
		add_definitions(-Wall -Wextra)
	endif()
	
	# 设置字符集为 Unicode
	add_compile_definitions(UNICODE)
	add_compile_definitions(_UNICODE)
	
	#0：禁用所有迭代器调试功能。
	#这是默认设置，用于发布版本（Release），此时迭代器不包含额外的调试检查，以提高性能。
	# 1：启用基本的迭代器调试功能
	# 2：启用完整的迭代器调试功能 调试版本（Debug
	# 设置运行时库配置
	add_definitions(-D_ITERATOR_DEBUG_LEVEL=0)
	
	add_library (${PROJECT_NAME} SHARED
		windows/usbmanager.cpp
		dllmain.cpp
		PluginRegister.cpp		
		${CMAKE_CURRENT_SOURCE_DIR}/public/nxcom/atcom/atdllexports.def
		${COMMON_SOURCE}
		${DEVCON_SOURCE}
	)
	
	# 获取 RELEASE,VERSION,BUILD
	execute_process(COMMAND git show -s --pretty=format:%h OUTPUT_VARIABLE BUILD)
	execute_process(COMMAND  git rev-parse --abbrev-ref HEAD OUTPUT_VARIABLE RELEASE_HEAD_OUT )
	string(STRIP ${RELEASE_HEAD_OUT} RELEASE_HEAD)
	string(STRIP ${RELEASE_HEAD}-${BUILD} RELEASE)
	string(TIMESTAMP VERSION "2.0.%y%m%d.%H%M")
	message("BUILD=${BUILD} RELEASE=${RELEASE} VERSION=${VERSION}")
	
	add_custom_target(
		make_usbprotector ALL
		echo "make usbprotector dll"
		COMMAND ${CMAKE_COMMAND} -DCALL=WinSign -DAT_NOSIGN=${AT_NOSIGN} -DARG1=${CMAKE_CURRENT_BINARY_DIR}/Bin/Release/usbprotector.dll -DARG2=${CMAKE_CURRENT_BINARY_DIR}/sign -P ${CMAKE_CURRENT_SOURCE_DIR}/base.cmake	
		
		COMMAND echo "copy over"
		echo "make usbprotector ap package"
		COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_CURRENT_BINARY_DIR}/pkg
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/ACSP
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/files
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin
		COMMAND ${CMAKE_COMMAND} -E make_directory   ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/Bin/Release/usbprotector.dll ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/usbprotector/usbprotector.atcom ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/usbprotector/usbprotector.atmon ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		
		#COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/usbprotector.db ${CMAKE_CURRENT_BINARY_DIR}/pkg/files/bin/usbprotector
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/template/control.windows ${CMAKE_CURRENT_BINARY_DIR}/pkg/ACSP/control
		COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_SOURCE_DIR}/template/service.windows ${CMAKE_CURRENT_BINARY_DIR}/pkg/ACSP/service
		#打包
		COMMAND acspacker ap --path=${CMAKE_CURRENT_BINARY_DIR}/pkg --out=${CMAKE_CURRENT_BINARY_DIR}/usbprotector.ap --ver=${VERSION} --git=${RELEASE}	
	)
	add_dependencies(make_usbprotector ${PROJECT_NAME})
endif()



