#ifndef USB_HOTPLUG_MONITOR_H
#define USB_HOTPLUG_MONITOR_H

#include <string>
#include <thread>
#include <atomic>
#include "usb_device_manager.h"

struct udev;
struct udev_monitor;
struct udev_device;

class USBHotplugMonitor
{
public:
    USBHotplugMonitor();
    ~USBHotplugMonitor();

    void StartMonitor();
    void StopMonitor();

    usb_manager::UsbDevice GetNotifyUSBDevInfo();
    bool DisableDevice(const usb_manager::UsbDevice& device);
    bool EnableDevice(const usb_manager::UsbDevice& device);
    int DisableTargetDevices();
    int EnableTargetDevices();

private:
    bool initialize();
    void start();
    void stop();

    void cleanup();
    void monitorLoop();
    void handleUdevEvent();
    bool isUSBStorageDevice(struct udev_device *device);
    void printDeviceInfo(struct udev_device *device);

private:
    struct udev *udev_;
    struct udev_monitor *monitor_;
    int monitor_fd_;
    std::atomic<bool> running_;
    std::thread monitor_thread_;
    usb_manager::UsbDeviceManager usb_dev_manager_;
};

#endif // USB_HOTPLUG_MONITOR_H