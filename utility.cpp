#include "utility.h"
#include <fstream>
#include <regex>
#include "public/qlogger.h"
#include "macro_defs.h"
#include <sstream>

std::string ReadFileToStr(const std::string& path)
{
	std::ifstream read_file;
	read_file.open(path, std::ios::binary);
	if (!read_file.is_open())
	{
		QLogErrorUtf8(LOG_NAME, "Can not open this file: [%s]", path.c_str());
		return "";
	}
	std::string str((std::istreambuf_iterator<char>(read_file)), std::istreambuf_iterator<char>());
	read_file.close();
	return str;
}

std::size_t FindAllMatches(const std::string& input, const std::string& pattern, std::vector<std::string>& matches)
{
	std::regex re(pattern);
	std::sregex_iterator it(input.begin(), input.end(), re);
	std::sregex_iterator end;
	for (; it != end; ++it)
	{
		matches.push_back(it->str());
	}
	return matches.size();
}

const char* CmdKindToStr(cmd_kind kind)
{
	const char* temp_str = "";
	switch (kind)
	{
	case Cmd_Set_host_first:
		temp_str = TEXT_TO_STR(Cmd_Set_host_first);
		break;
	case Cmd_Clear_host_first:
		temp_str = TEXT_TO_STR(Cmd_Clear_host_first);
		break;
	case Cmd_Update_FireWall_Client_id:
		temp_str = TEXT_TO_STR(Cmd_Update_FireWall_Client_id);
		break;
	case Cmd_FireWall_Start:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Start);
		break;
	case Cmd_FireWall_Stop:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Stop);
		break;
	case Cmd_Add_IP_Rules:
		temp_str = TEXT_TO_STR(Cmd_Add_IP_Rules);
		break;
	case Cmd_Delete_IP_Rules:
		temp_str = TEXT_TO_STR(Cmd_Delete_IP_Rules);
		break;
	case Cmd_Add_Port_Rules:
		temp_str = TEXT_TO_STR(Cmd_Add_Port_Rules);
		break;
	case Cmd_Delete_Port_Rules:
		temp_str = TEXT_TO_STR(Cmd_Delete_Port_Rules);
		break;
	case Cmd_Set_Port_Rule_Black:
		temp_str = TEXT_TO_STR(Cmd_Set_Port_Rule_Black);
		break;
	case Cmd_Clear_Port_Rule_Black:
		temp_str = TEXT_TO_STR(Cmd_Clear_Port_Rule_Black);
		break;
	case Cmd_Set_Recv_Port_Scan:
		temp_str = TEXT_TO_STR(Cmd_Set_Recv_Port_Scan);
		break;
	case Cmd_Clear_Recv_Port_Scan:
		temp_str = TEXT_TO_STR(Cmd_Clear_Recv_Port_Scan);
		break;
	case Cmd_Set_Send_Port_Scan:
		temp_str = TEXT_TO_STR(Cmd_Set_Send_Port_Scan);
		break;
	case Cmd_Clear_Send_Port_Scan:
		temp_str = TEXT_TO_STR(Cmd_Clear_Send_Port_Scan);
		break;
	case Cmd_Add_Mac_Rules:
		temp_str = TEXT_TO_STR(Cmd_Add_Mac_Rules);
		break;
	case Cmd_Delete_Mac_Rules:
		temp_str = TEXT_TO_STR(Cmd_Delete_Mac_Rules);
		break;
	case Cmd_Traffic_Statistics_Start:
		temp_str = TEXT_TO_STR(Cmd_Traffic_Statistics_Start);
		break;
	case Cmd_Traffic_Statistics_Stop:
		temp_str = TEXT_TO_STR(Cmd_Traffic_Statistics_Stop);
		break;
	case Cmd_FireWall_White_Frist:
		temp_str = TEXT_TO_STR(Cmd_FireWall_White_Frist);
		break;
	case Cmd_FireWall_Black_Frist:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Black_Frist);
		break;
	case Cmd_FireWall_Set_White_Rules:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Set_White_Rules);
		break;
	case Cmd_FireWall_Clear_White_Rules:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Clear_White_Rules);
		break;
	case Cmd_FireWall_Set_Black_Rules:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Set_Black_Rules);
		break;
	case Cmd_FireWall_Clear_Black_Rules:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Clear_Black_Rules);
		break;
	case Cmd_FireWall_In_Set_Pass_No_Policy:
		temp_str = TEXT_TO_STR(Cmd_FireWall_In_Set_Pass_No_Policy);
		break;
	case Cmd_FireWall_In_Set_Deny_No_Policy:
		temp_str = TEXT_TO_STR(Cmd_FireWall_In_Set_Deny_No_Policy);
		break;
	case Cmd_FireWall_Out_Set_Pass_No_Policy:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Out_Set_Pass_No_Policy);
		break;
	case Cmd_FireWall_Out_Set_Deny_No_Policy:
		temp_str = TEXT_TO_STR(Cmd_FireWall_Out_Set_Deny_No_Policy);
		break;
	case Cmd_Set_Time_Offset:
		temp_str = TEXT_TO_STR(Cmd_Set_Time_Offset);
		break;
	case Cmd_Get_Message:
		temp_str = TEXT_TO_STR(Cmd_Get_Message);
		break;
	case Cmd_Set_Message:
		temp_str = TEXT_TO_STR(Cmd_Set_Message);
		break;
	case Cmd_Create_Group:
		temp_str = TEXT_TO_STR(Cmd_Create_Group);
		break;
	case Cmd_Proc_To_Group:
		temp_str = TEXT_TO_STR(Cmd_Proc_To_Group);
		break;
	case Cmd_Update_Config:
		temp_str = TEXT_TO_STR(Cmd_Update_Config);
		break;
	case Cmd_Delete_Config:
		temp_str = TEXT_TO_STR(Cmd_Delete_Config);
		break;
	case Cmd_Get_Comm_State:
		temp_str = TEXT_TO_STR(Cmd_Get_Comm_State);
		break;
	case Cmd_Wakeup_Comm:
		temp_str = TEXT_TO_STR(Cmd_Wakeup_Comm);
		break;
	case Cmd_Add_Pid_Whitelist:
		temp_str = TEXT_TO_STR(Cmd_Add_Pid_Whitelist);
		break;
	case Cmd_Del_Pid_Whitelist:
		temp_str = TEXT_TO_STR(Cmd_Del_Pid_Whitelist);
		break;
	case Cmd_Set_Queue_Depth:
		temp_str = TEXT_TO_STR(Cmd_Set_Queue_Depth);
		break;
	case Cmd_Add_Filter_Notify:
		temp_str = TEXT_TO_STR(Cmd_Add_Filter_Notify);
		break;
	case Cmd_Del_Filter_Notify:
		temp_str = TEXT_TO_STR(Cmd_Del_Filter_Notify);
		break;
	case Cmd_Update_Net_Policy:
		temp_str = TEXT_TO_STR(Cmd_Update_Net_Policy);
		break;
	case Cmd_Delete_Net_Policy:
		temp_str = TEXT_TO_STR(Cmd_Delete_Net_Policy);
		break;
	case Cmd_Update_White_Net_Policy:
		temp_str = TEXT_TO_STR(Cmd_Update_White_Net_Policy);
		break;
	case Cmd_Delete_White_Net_Policy:
		temp_str = TEXT_TO_STR(Cmd_Delete_White_Net_Policy);
		break;
	case Cmd_Set_White_Policy_First:
		temp_str = TEXT_TO_STR(Cmd_Set_White_Policy_First);
		break;
	case Cmd_Clear_White_Policy_First:
		temp_str = TEXT_TO_STR(Cmd_Clear_White_Policy_First);
		break;
	case Cmd_Add_Client_Label:
		temp_str = TEXT_TO_STR(Cmd_Add_Client_Label);
		break;
	case Cmd_Update_Client_Label:
		temp_str = TEXT_TO_STR(Cmd_Update_Client_Label);
		break;
	case Cmd_Delete_Client_Label:
		temp_str = TEXT_TO_STR(Cmd_Delete_Client_Label);
		break;
	case Cmd_Update_Agent_Guid:
		temp_str = TEXT_TO_STR(Cmd_Update_Agent_Guid);
		break;
	case Cmd_Update_load_id:
		temp_str = TEXT_TO_STR(Cmd_Update_load_id);
		break;
	case Cmd_Update_Client_id:
		temp_str = TEXT_TO_STR(Cmd_Update_Client_id);
		break;
	case Cmd_Update_isolate_status:
		temp_str = TEXT_TO_STR(Cmd_Update_isolate_status);
		break;
	case Cmd_Delete_isolate_status:
		temp_str = TEXT_TO_STR(Cmd_Delete_isolate_status);
		break;
	case Cmd_Update_pid_port:
		temp_str = TEXT_TO_STR(Cmd_Update_pid_port);
		break;
	case Cmd_Add_IP_Black_List:
		temp_str = TEXT_TO_STR(Cmd_Add_IP_Black_List);
		break;
	case Cmd_Delete_IP_Black_List:
		temp_str = TEXT_TO_STR(Cmd_Delete_IP_Black_List);
		break;
	case Cmd_Add_IP_White_List:
		temp_str = TEXT_TO_STR(Cmd_Add_IP_White_List);
		break;
	case Cmd_Delete_IP_White_List:
		temp_str = TEXT_TO_STR(Cmd_Delete_IP_White_List);
		break;
	case Cmd_Add_APP_White_List:
		temp_str = TEXT_TO_STR(Cmd_Add_APP_White_List);
		break;
	case Cmd_Delete_APP_White_List:
		temp_str = TEXT_TO_STR(Cmd_Delete_APP_White_List);
		break;
	case Cmd_Policy_Switch_Start:
		temp_str = TEXT_TO_STR(Cmd_Policy_Switch_Start);
		break;
	case Cmd_Policy_Switch_Stop:
		temp_str = TEXT_TO_STR(Cmd_Policy_Switch_Stop);
		break;
	case Cmd_UDP_Auth_Start:
		temp_str = TEXT_TO_STR(Cmd_UDP_Auth_Start);
		break;
	case Cmd_UDP_Auth_Stop:
		temp_str = TEXT_TO_STR(Cmd_UDP_Auth_Stop);
		break;
	case Cmd_UDP_Log_Start:
		temp_str = TEXT_TO_STR(Cmd_UDP_Log_Start);
		break;
	case Cmd_UDP_Log_Stop:
		temp_str = TEXT_TO_STR(Cmd_UDP_Log_Stop);
		break;
	case Cmd_Set_Isolate_Start:
		temp_str = TEXT_TO_STR(Cmd_Set_Isolate_Start);
		break;
	case Cmd_Set_Isolate_Stop:
		temp_str = TEXT_TO_STR(Cmd_Set_Isolate_Stop);
		break;
	case Cmd_Set_Tcp_Option_Value:
		temp_str = TEXT_TO_STR(Cmd_Set_Tcp_Option_Value);
		break;
	case Cmd_Set_Is_Clear_Auth_Info:
		temp_str = TEXT_TO_STR(Cmd_Set_Is_Clear_Auth_Info);
		break;
	case Cmd_Set_Udp_Ip_List:
		temp_str = TEXT_TO_STR(Cmd_Set_Udp_Ip_List);
		break;
	case Cmd_Clear_Udp_Ip_List:
		temp_str = TEXT_TO_STR(Cmd_Clear_Udp_Ip_List);
		break;
	case Cmd_IPV4_Auth_Start:
		temp_str = TEXT_TO_STR(Cmd_IPV4_Auth_Start);
		break;
	case Cmd_IPV4_Auth_Stop:
		temp_str = TEXT_TO_STR(Cmd_IPV4_Auth_Stop);
		break;
	case Cmd_IPV6_Auth_Start:
		temp_str = TEXT_TO_STR(Cmd_IPV6_Auth_Start);
		break;
	case Cmd_IPV6_Auth_Stop:
		temp_str = TEXT_TO_STR(Cmd_IPV6_Auth_Stop);
		break;
	case Cmd_TCP_Auth_Start:
		temp_str = TEXT_TO_STR(Cmd_TCP_Auth_Start);
		break;
	case Cmd_TCP_Auth_Stop:
		temp_str = TEXT_TO_STR(Cmd_TCP_Auth_Stop);
		break;
	case Cmd_Update_Virtual_Client_id:
		temp_str = TEXT_TO_STR(Cmd_Update_Virtual_Client_id);
		break;
	case Cmd_Set_Suricata_Start:
		temp_str = TEXT_TO_STR(Cmd_Set_Suricata_Start);
		break;
	case Cmd_Clear_Suricata_Start:
		temp_str = TEXT_TO_STR(Cmd_Clear_Suricata_Start);
		break;
	case Cmd_Set_Suricata_Rules:
		temp_str = TEXT_TO_STR(Cmd_Set_Suricata_Rules);
		break;
	case Cmd_Clear_Suricata_Rules:
		temp_str = TEXT_TO_STR(Cmd_Clear_Suricata_Rules);
		break;
	case Cmd_Issue_Rules_File_Buffer:
		temp_str = TEXT_TO_STR(Cmd_Issue_Rules_File_Buffer);
		break;
	case Cmd_Restart_Init_Rules_File:
		temp_str = TEXT_TO_STR(Cmd_Restart_Init_Rules_File);
		break;
	case Cmd_Set_Vpatch_Port_White:
		temp_str = TEXT_TO_STR(Cmd_Set_Vpatch_Port_White);
		break;
	case Cmd_Clear_Vpatch_Port_White:
		temp_str = TEXT_TO_STR(Cmd_Clear_Vpatch_Port_White);
		break;
	case Cmd_Set_Flow_Timeout_Sec:
		temp_str = TEXT_TO_STR(Cmd_Set_Flow_Timeout_Sec);
		break;
	case Cmd_Set_Flow_Handle_Max_Packet:
		temp_str = TEXT_TO_STR(Cmd_Set_Flow_Handle_Max_Packet);
		break;
	case Cmd_Set_Port_Scan_WhiteList:
		temp_str = TEXT_TO_STR(Cmd_Set_Port_Scan_WhiteList);
		break;
	case Cmd_Clear_Port_Scan_WhiteList:
		temp_str = TEXT_TO_STR(Cmd_Clear_Port_Scan_WhiteList);
		break;
	default:
		temp_str = "unknow_cmd_kind";
		break;
	}
	return temp_str;
}

int SplitString(const std::string& input,
	char delimiter,
	std::vector<std::string>* result)
{
	if (!result)
	return 0;
	result->clear();
	std::stringstream ss(input);
	std::string token;
	while (std::getline(ss, token, delimiter))
	{
		if (!token.empty())
		{
			result->push_back(token);
		}
	}
	return result->size();
}
