#include "usb_device_enum.h"
#include "qlogger.h"
#include "macro_defs.h"

#include <initguid.h>
#include <usbiodef.h>
#include <setupapi.h>
#include <iostream>
#include <usbioctl.h>
#include <cfgmgr32.h>
#include <vector>
#include <string>

#pragma comment(lib, "setupapi.lib")
// #pragma comment(lib, "cfgmgr32.lib")

namespace usb_device
{

    // 将 vendor_id 转换为 0x%04X 格式的字符串
    std::string DeviceInfo::VendorIdToString() const
    {
        char buffer[7]; // 0x + 4个字符 + null terminator
        sprintf(buffer, "0x%04X", vendor_id);
        return std::string(buffer);
    }

    // 将 product_id 转换为 0x%04X 格式的字符串
    std::string DeviceInfo::ProductIdToString() const
    {
        char buffer[7]; // 0x + 4个字符 + null terminator
        sprintf(buffer, "0x%04X", product_id);
        return std::string(buffer);
    }

    // 从硬件ID解析VID和PID
    bool ParseVidPid(const std::wstring &hardware_id, uint16_t &vid, uint16_t &pid)
    {
        size_t vid_pos = hardware_id.find(L"VID_");
        size_t pid_pos = hardware_id.find(L"PID_");

        if (vid_pos != std::wstring::npos && vid_pos + 8 <= hardware_id.length())
        {
            vid = static_cast<uint16_t>(wcstoul(hardware_id.substr(vid_pos + 4, 4).c_str(), nullptr, 16));
        }
        if (pid_pos != std::wstring::npos && pid_pos + 8 <= hardware_id.length())
        {
            pid = static_cast<uint16_t>(wcstoul(hardware_id.substr(pid_pos + 4, 4).c_str(), nullptr, 16));
        }

        return (vid != 0 || pid != 0);
    }

    std::wstring ExtractSerialNumber(const std::wstring &instance_id)
    {
        size_t last_backslash = instance_id.rfind(L'\\');
        if (last_backslash != std::wstring::npos)
        {
            std::wstring serial = instance_id.substr(last_backslash + 1);
            if (!serial.empty() && serial != L"0" && serial.find(L'&') == std::wstring::npos)
            {
                return serial;
            }
        }
        return L"";
    }

    std::wstring GetDeviceProperty(HDEVINFO dev_info, SP_DEVINFO_DATA *dev_data, DWORD property)
    {
        WCHAR buffer[1024] = {0};
        if (SetupDiGetDeviceRegistryPropertyW(dev_info, dev_data, property, nullptr,
                                              reinterpret_cast<PBYTE>(buffer),
                                              sizeof(buffer), nullptr))
        {
            return buffer;
        }
        return L"";
    }

    // 通过设备实例句柄获取设备属性
    std::wstring GetDevicePropertyByDevinst(DEVINST dev_inst, ULONG property)
    {
        ULONG buffer_size = 0;
        CONFIGRET cr = CM_Get_DevNode_Registry_PropertyW(dev_inst, property, nullptr,
                                                         nullptr, &buffer_size, 0);

        if (cr != CR_BUFFER_SMALL)
        {
            return L"";
        }

        std::vector<WCHAR> buffer(buffer_size / sizeof(WCHAR));
        cr = CM_Get_DevNode_Registry_PropertyW(dev_inst, property, nullptr,
                                               buffer.data(), &buffer_size, 0);

        if (cr == CR_SUCCESS)
        {
            return std::wstring(buffer.data());
        }
        return L"";
    }

    // 获取设备实例ID
    std::wstring GetDeviceInstanceId(DEVINST dev_inst)
    {
        WCHAR instance_id[MAX_DEVICE_ID_LEN] = {0};
        if (CM_Get_Device_IDW(dev_inst, instance_id, MAX_DEVICE_ID_LEN, 0) == CR_SUCCESS)
        {
            return instance_id;
        }
        return L"";
    }

    // 检查设备是否为USB设备
    bool IsUsbDevice(const std::wstring &instance_id)
    {
        std::wstring id_upper = instance_id;
        std::transform(id_upper.begin(), id_upper.end(), id_upper.begin(), ::towupper);
        return (id_upper.find(L"USB\\") == 0 || id_upper.find(L"USBSTOR\\") == 0);
    }

    // 检查设备是否启用
    bool IsDeviceEnabled(HDEVINFO dev_info, PSP_DEVINFO_DATA dev_data)
    {
        DWORD status = 0;
        DWORD problem = 0;
        if (CM_Get_DevNode_Status(&status, &problem, dev_data->DevInst, 0) != CR_SUCCESS)
        {
            return false;
        }

        // 检查设备是否已启用
        return (status & DN_HAS_PROBLEM) == 0;
    }

    // 获取所有 hub 设备路径
    std::vector<std::wstring> EnumUsbHubs()
    {
        std::vector<std::wstring> hubs;
        HDEVINFO dev_info = SetupDiGetClassDevsW(&GUID_DEVINTERFACE_USB_HUB, nullptr, nullptr, DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);
        if (dev_info == INVALID_HANDLE_VALUE)
            return hubs;

        SP_DEVICE_INTERFACE_DATA if_data = {0};
        if_data.cbSize = sizeof(if_data);

        for (DWORD i = 0; SetupDiEnumDeviceInterfaces(dev_info, nullptr, &GUID_DEVINTERFACE_USB_HUB, i, &if_data); ++i)
        {
            DWORD required = 0;
            SetupDiGetDeviceInterfaceDetailW(dev_info, &if_data, nullptr, 0, &required, nullptr);
            std::vector<BYTE> buf(required);
            auto detail = reinterpret_cast<SP_DEVICE_INTERFACE_DETAIL_DATA_W *>(&buf[0]);
            detail->cbSize = sizeof(SP_DEVICE_INTERFACE_DETAIL_DATA_W);
            if (SetupDiGetDeviceInterfaceDetailW(dev_info, &if_data, detail, required, nullptr, nullptr))
            {
                hubs.push_back(detail->DevicePath);
            }
        }
        SetupDiDestroyDeviceInfoList(dev_info);
        return hubs;
    }

    // 通过 hub 路径和端口号获取字符串描述符
    std::wstring GetUsbStringDescriptor(HANDLE hub_handle, ULONG port, UCHAR desc_index, USHORT langid = 0x0409)
    {
        if (desc_index == 0)
            return L"";
        USB_DESCRIPTOR_REQUEST req = {0};
        req.ConnectionIndex = port;
        req.SetupPacket.wValue = (USB_STRING_DESCRIPTOR_TYPE << 8) | desc_index;
        req.SetupPacket.wIndex = langid;
        req.SetupPacket.wLength = 255;
        req.SetupPacket.bmRequest = 0x80; // Device-to-host
        req.SetupPacket.bRequest = USB_REQUEST_GET_DESCRIPTOR;
        req.SetupPacket.wLength = 255;

        BYTE buffer[sizeof(USB_DESCRIPTOR_REQUEST) + 255] = {0};
        memcpy(buffer, &req, sizeof(USB_DESCRIPTOR_REQUEST));
        DWORD returned = 0;
        if (!DeviceIoControl(hub_handle, IOCTL_USB_GET_DESCRIPTOR_FROM_NODE_CONNECTION, buffer, sizeof(buffer), buffer, sizeof(buffer), &returned, nullptr))
        {
            return L"";
        }
        PUSB_STRING_DESCRIPTOR str_desc = (PUSB_STRING_DESCRIPTOR)(buffer + sizeof(USB_DESCRIPTOR_REQUEST));
        if (str_desc->bLength < 2)
            return L"";
        return std::wstring(str_desc->bString, (str_desc->bLength - 2) / 2);
    }

    // 通过 hub 路径和端口号获取设备描述符
    bool GetUsbDeviceDescriptor(HANDLE hub_handle, ULONG port, USB_DEVICE_DESCRIPTOR &desc)
    {
        USB_NODE_CONNECTION_INFORMATION_EX conn_info = {0};
        conn_info.ConnectionIndex = port;
        DWORD returned = 0;
        if (!DeviceIoControl(hub_handle, IOCTL_USB_GET_NODE_CONNECTION_INFORMATION_EX, &conn_info, sizeof(conn_info), &conn_info, sizeof(conn_info), &returned, nullptr))
        {
            return false;
        }
        if (conn_info.ConnectionStatus != DeviceConnected)
            return false;
        desc = conn_info.DeviceDescriptor;
        return true;
    }

    // 获取端口上的 DriverKeyName
    std::wstring GetPortDriverKeyName(HANDLE hub_handle, ULONG port)
    {
        USB_NODE_CONNECTION_DRIVERKEY_NAME driverkey = {0};
        driverkey.ConnectionIndex = port;
        BYTE buffer[sizeof(USB_NODE_CONNECTION_DRIVERKEY_NAME) + 512] = {0};
        memcpy(buffer, &driverkey, sizeof(USB_NODE_CONNECTION_DRIVERKEY_NAME));
        DWORD returned = 0;
        if (!DeviceIoControl(hub_handle, IOCTL_USB_GET_NODE_CONNECTION_DRIVERKEY_NAME,
                             buffer, sizeof(buffer), buffer, sizeof(buffer), &returned, nullptr))
        {
            return L"";
        }
        auto pName = (PUSB_NODE_CONNECTION_DRIVERKEY_NAME)buffer;
        return std::wstring(pName->DriverKeyName);
    }

    // 获取设备实例的 DriverKeyName
    std::wstring GetDeviceDriverKeyName(DEVINST dev_inst)
    {
        WCHAR keyname[512] = {0};
        ULONG size = sizeof(keyname);
        if (CM_Get_DevNode_Registry_PropertyW(dev_inst, CM_DRP_DRIVER, nullptr, keyname, &size, 0) == CR_SUCCESS)
        {
            return std::wstring(keyname);
        }
        return L"";
    }

    // 通过设备实例ID查找 hub 路径和端口号
    bool FindHubAndPortByInstanceId(DEVINST &instance_id, std::wstring &hub_path, ULONG &port)
    {
        auto dev_driverkey = GetDeviceDriverKeyName(instance_id);

        auto hubs = EnumUsbHubs();
        for (const auto &path : hubs)
        {
            HANDLE hub_handle = CreateFileW(path.c_str(), GENERIC_WRITE | GENERIC_READ, FILE_SHARE_WRITE | FILE_SHARE_READ, nullptr, OPEN_EXISTING, 0, nullptr);
            if (hub_handle == INVALID_HANDLE_VALUE)
                continue;

            USB_NODE_INFORMATION node_info = {0};
            node_info.NodeType = UsbHub;
            DWORD returned = 0;
            if (!DeviceIoControl(hub_handle, IOCTL_USB_GET_NODE_INFORMATION, &node_info, sizeof(node_info), &node_info, sizeof(node_info), &returned, nullptr))
            {
                CloseHandle(hub_handle);
                continue;
            }
            ULONG port_count = node_info.u.HubInformation.HubDescriptor.bNumberOfPorts;

            for (ULONG i = 1; i <= port_count; ++i)
            {
                USB_NODE_CONNECTION_NAME name = {0};
                name.ConnectionIndex = i;
                BYTE name_buf[sizeof(USB_NODE_CONNECTION_NAME) + 512] = {0};
                memcpy(name_buf, &name, sizeof(USB_NODE_CONNECTION_NAME));
                if (!DeviceIoControl(hub_handle, IOCTL_USB_GET_NODE_CONNECTION_NAME, name_buf, sizeof(name_buf), name_buf, sizeof(name_buf), &returned, nullptr))
                {
                    continue;
                }
                auto pName = (PUSB_NODE_CONNECTION_NAME)name_buf;
                std::wstring device_path = pName->NodeName;

                auto port_driverkey = GetPortDriverKeyName(hub_handle, i);

                // QLogInfoW(TEXT(LOG_NAME), L"FindHubAndPortByInstanceId port_driverkey: %ls dev_driverkey: %ls hub_path: %ls", port_driverkey.c_str(), dev_driverkey.c_str(), path.c_str());

                // 设备路径中包含设备实例ID
                if (!port_driverkey.empty() && !dev_driverkey.empty() &&
                    port_driverkey == dev_driverkey)
                {
                    // 关联成功，hub_path/port 就是你要的
                    hub_path = path;
                    port = i;
                    CloseHandle(hub_handle);
                    return true;
                }
            }
            CloseHandle(hub_handle);
        }
        return false;
    }

    bool FillDeviceInfo(DEVINST &instance_id, DeviceInfo &info)
    {
        std::wstring hub_path;
        ULONG port = 0;
        if (FindHubAndPortByInstanceId(instance_id, hub_path, port))
        {
            // QLogInfoW(TEXT(LOG_NAME), L"hub hub_path: %ls port: %d", hub_path.c_str(), port);
            HANDLE hub_handle = CreateFileW(hub_path.c_str(), GENERIC_WRITE | GENERIC_READ, FILE_SHARE_WRITE | FILE_SHARE_READ, nullptr, OPEN_EXISTING, 0, nullptr);
            if (hub_handle != INVALID_HANDLE_VALUE)
            {
                USB_DEVICE_DESCRIPTOR desc = {0};
                if (GetUsbDeviceDescriptor(hub_handle, port, desc))
                {
                    std::wstring manufacturer = GetUsbStringDescriptor(hub_handle, port, desc.iManufacturer);
                    std::wstring product = GetUsbStringDescriptor(hub_handle, port, desc.iProduct);

                    if (!manufacturer.empty())
                        info.manufacturer = manufacturer;
                    if (!product.empty())
                        info.product_name = product;
                }
                CloseHandle(hub_handle);
                return true;
            }
        }
        return false;
    }

    // 从父设备链中查找USB设备信息
    void FillUsbInfoFromParents(DEVINST dev_inst, DeviceInfo &info)
    {
        DEVINST current_inst = dev_inst;
        DEVINST parent_inst = 0;

        // 向上遍历父设备链，最多查找5层
        for (int level = 0; level < 5; ++level)
        {
            // 获取父设备
            if (CM_Get_Parent(&parent_inst, current_inst, 0) != CR_SUCCESS)
            {
                break;
            }

            // 获取父设备的实例ID
            std::wstring parent_instance_id = GetDeviceInstanceId(parent_inst);
            if (parent_instance_id.empty())
            {
                current_inst = parent_inst;
                continue;
            }

            // 检查是否为USB设备
            if (0 == _wcsnicmp(parent_instance_id.c_str(), L"USB\\", 4))
            {
                // 获取硬件ID
                std::wstring parent_hardware_id = GetDevicePropertyByDevinst(parent_inst, CM_DRP_HARDWAREID);

                // 如果当前设备没有VID/PID，尝试从父设备获取
                if (info.vendor_id == 0 && info.product_id == 0 && !parent_hardware_id.empty())
                {
                    ParseVidPid(parent_hardware_id, info.vendor_id, info.product_id);
                }

                // 如果当前设备没有序列号，尝试从父设备获取
                if (info.serial_number.empty())
                {
                    std::wstring serial = ExtractSerialNumber(parent_instance_id);
                    if (!serial.empty())
                    {
                        info.serial_number = serial;
                    }
                }

                if (FillDeviceInfo(parent_inst, info))
                {
                    break;
                }
            }
            current_inst = parent_inst;
        }
    }

    // 检查是否为目标设备类型
    bool IsTargetDeviceClass(DeviceClass device_class)
    {
        return device_class != DeviceClass::Unknown;
    }

    // 将设备类字符串转换为枚举
    DeviceClass StringToDeviceClass(const std::wstring &device_class_str)
    {
        if (device_class_str == L"DiskDrive")
        {
            return DeviceClass::DiskDrive;
        }
        else if (device_class_str == L"CDROM")
        {
            return DeviceClass::CDROM;
        }
        else if (device_class_str == L"Net")
        {
            return DeviceClass::Net;
        }
        else if (device_class_str == L"WPD")
        {
            return DeviceClass::WPD;
        }
        return DeviceClass::Unknown;
    }

    // 将 vendor_id 转换为 0x%04X 格式的字符串
    std::wstring VendorIdToString(uint16_t vendor_id)
    {
        wchar_t buffer[7]; // 0x + 4个字符 + null terminator
        swprintf(buffer, 7, L"0x%04X", vendor_id);
        return std::wstring(buffer);
    }

    // 将 product_id 转换为 0x%04X 格式的字符串
    std::wstring ProductIdToString(uint16_t product_id)
    {
        wchar_t buffer[7]; // 0x + 4个字符 + null terminator
        swprintf(buffer, 7, L"0x%04X", product_id);
        return std::wstring(buffer);
    }

    // 枚举USB设备
    std::vector<DeviceInfo> EnumerateUsbDevices()
    {
        std::vector<DeviceInfo> devices;

        HDEVINFO dev_info = SetupDiGetClassDevs(nullptr, nullptr, nullptr,
                                                DIGCF_PRESENT | DIGCF_ALLCLASSES);
        if (dev_info == INVALID_HANDLE_VALUE)
        {
            return devices;
        }

        SP_DEVINFO_DATA dev_data = {};
        dev_data.cbSize = sizeof(SP_DEVINFO_DATA);

        for (DWORD i = 0; SetupDiEnumDeviceInfo(dev_info, i, &dev_data); ++i)
        {
            // 获取设备类
            std::wstring device_class = GetDeviceProperty(dev_info, &dev_data, SPDRP_CLASS);

            // 检查是否为目标设备类型
            if (!IsTargetDeviceClass(StringToDeviceClass(device_class)))
            {
                continue;
            }

            DeviceInfo info;
            info.device_class = StringToDeviceClass(device_class);

            // 获取设备实例ID
            WCHAR instance_id_buffer[MAX_DEVICE_ID_LEN] = {0};
            if (!SetupDiGetDeviceInstanceIdW(dev_info, &dev_data, instance_id_buffer,
                                             MAX_DEVICE_ID_LEN, nullptr))
            {
                continue;
            }
            info.instance_id = instance_id_buffer;

            // 获取硬件ID
            info.hardware_id = GetDeviceProperty(dev_info, &dev_data, SPDRP_HARDWAREID);

            // 首先检查当前设备是否直接是USB设备
            bool is_direct_usb = IsUsbDevice(info.instance_id);

            if (!is_direct_usb)
                continue;

            // 获取产品名称
            info.product_name = GetDeviceProperty(dev_info, &dev_data, SPDRP_DEVICEDESC);
            if (info.product_name.empty())
            {
                info.product_name = GetDeviceProperty(dev_info, &dev_data, SPDRP_FRIENDLYNAME);
            }

            // 获取制造商
            info.manufacturer = GetDeviceProperty(dev_info, &dev_data, SPDRP_MFG);

            // 尝试从当前设备解析VID/PID和序列号
            ParseVidPid(info.hardware_id, info.vendor_id, info.product_id);
            info.serial_number = ExtractSerialNumber(info.instance_id);

            if (_wcsnicmp(info.instance_id.c_str(), L"USB\\", 4) == 0)
            {
                FillDeviceInfo(dev_data.DevInst, info);
            }
            else
            {
                // 从父设备链补充USB信息
                FillUsbInfoFromParents(dev_data.DevInst, info);
            }

            // 检查设备是否启用
            info.is_enabled = IsDeviceEnabled(dev_info, &dev_data);
            devices.push_back(info);
        }

        SetupDiDestroyDeviceInfoList(dev_info);
        return devices;
    }

    // 通过设备实例ID获取设备信息
    DeviceInfo GetDeviceInfoByInstanceId(const std::wstring &instance_id)
    {
        DeviceInfo info;
        info.instance_id = instance_id;

        // 创建设备信息列表
        HDEVINFO dev_info = SetupDiGetClassDevs(nullptr, nullptr, nullptr,
                                                DIGCF_PRESENT | DIGCF_ALLCLASSES);
        if (dev_info == INVALID_HANDLE_VALUE)
        {
            return info;
        }

        SP_DEVINFO_DATA dev_data = {};
        dev_data.cbSize = sizeof(SP_DEVINFO_DATA);

        // 遍历设备查找匹配的实例ID
        for (DWORD i = 0; SetupDiEnumDeviceInfo(dev_info, i, &dev_data); ++i)
        {
            // 获取设备实例ID
            WCHAR device_instance_id[MAX_DEVICE_ID_LEN] = {0};
            if (!SetupDiGetDeviceInstanceIdW(dev_info, &dev_data, device_instance_id,
                                             MAX_DEVICE_ID_LEN, nullptr))
            {
                continue;
            }

            // 检查是否匹配
            if (0 == _wcsicmp(device_instance_id, instance_id.c_str()))
            {
                // 获取设备类
                std::wstring device_class = GetDeviceProperty(dev_info, &dev_data, SPDRP_CLASS);
                info.device_class = StringToDeviceClass(device_class);

                // 获取硬件ID
                info.hardware_id = GetDeviceProperty(dev_info, &dev_data, SPDRP_HARDWAREID);

                // 获取产品名称
                info.product_name = GetDeviceProperty(dev_info, &dev_data, SPDRP_DEVICEDESC);
                if (info.product_name.empty())
                {
                    info.product_name = GetDeviceProperty(dev_info, &dev_data, SPDRP_FRIENDLYNAME);
                }

                // 获取制造商
                info.manufacturer = GetDeviceProperty(dev_info, &dev_data, SPDRP_MFG);

                // 尝试从当前设备解析VID/PID和序列号
                ParseVidPid(info.hardware_id, info.vendor_id, info.product_id);
                info.serial_number = ExtractSerialNumber(info.instance_id);

                // 从父设备链补充USB信息
                FillUsbInfoFromParents(dev_data.DevInst, info);

                // 检查设备是否启用
                info.is_enabled = IsDeviceEnabled(dev_info, &dev_data);

                break;
            }
        }

        SetupDiDestroyDeviceInfoList(dev_info);
        return info;
    }

    // 打印设备信息
    void PrintDeviceInfo(const DeviceInfo &device)
    {
        std::wcout << L"----------------------------------------\n";
        std::wcout << L"设备类型: ";
        switch (device.device_class)
        {
        case DeviceClass::DiskDrive:
            std::wcout << L"DiskDrive";
            break;
        case DeviceClass::CDROM:
            std::wcout << L"CDROM";
            break;
        case DeviceClass::Net:
            std::wcout << L"Net";
            break;
        case DeviceClass::WPD:
            std::wcout << L"WPD";
            break;
        default:
            std::wcout << L"Unknown";
            break;
        }
        std::wcout << L"\n";
        std::wcout << L"产品名称: " << device.product_name << L"\n";
        std::wcout << L"制造商: " << device.manufacturer << L"\n";
        std::wcout << L"硬件ID: " << device.hardware_id << L"\n";
        std::wcout << L"实例ID: " << device.instance_id << L"\n";
        std::wcout << L"VID: " << VendorIdToString(device.vendor_id) << L"\n";
        std::wcout << L"PID: " << ProductIdToString(device.product_id) << L"\n";
        std::wcout << L"序列号: " << (device.serial_number.empty() ? L"N/A" : device.serial_number) << L"\n";
        std::wcout << L"启用状态: " << (device.is_enabled ? L"启用" : L"禁用") << L"\n";
    }

} // namespace usb_device