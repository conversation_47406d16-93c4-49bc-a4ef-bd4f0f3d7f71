﻿// dllmain.cpp : 定义 DLL 应用程序的入口点。
#if defined(WINDOWS_COMPILE)
#include "pch.h"
#endif
#include "PluginRegister.h"

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

BEGIN_CLID_MAP()
   CLID_MAP_ENTRY(CLSID_USBProtector_Plugin, PluginRegister, "usbprotector")
END_CLID_MAP()
