#include "usb_hotplug_monitor.h"
#include <iostream>
#include <fstream>
#include <cstring>
#include <unistd.h>
#include <sys/select.h>
#include <libudev.h>
#include <algorithm>
#include <signal.h>
#include "utilset/blocking_queue.hpp"
#include "qlogger.h"
#include "macro_defs.h"

using std::string;

static BlockingQueue<usb_manager::UsbDevice> kUSBNotifyQueue;

USBHotplugMonitor::USBHotplugMonitor() 
    : udev_(nullptr), monitor_(nullptr), monitor_fd_(-1), running_(false) {}

USBHotplugMonitor::~USBHotplugMonitor() {
    stop();
    cleanup();
}

bool USBHotplugMonitor::initialize() {
    udev_ = udev_new();
    if (!udev_) {
        QLogInfoUtf8(LOG_NAME, "Failed to create udev context");
        return false;
    }

    monitor_ = udev_monitor_new_from_netlink(udev_, "udev");
    if (!monitor_) {
        QLogInfoUtf8(LOG_NAME, "Failed to create udev monitor");
        return false;
    }

    if (udev_monitor_filter_add_match_subsystem_devtype(monitor_, "usb", "usb_interface") < 0) {
        QLogInfoUtf8(LOG_NAME, "Failed to add subsystem filter");
        return false;
    }

    if (udev_monitor_enable_receiving(monitor_) < 0) {
        QLogInfoUtf8(LOG_NAME, "Failed to enable monitor receiving");
        return false;
    }

    monitor_fd_ = udev_monitor_get_fd(monitor_);
    if (monitor_fd_ < 0) {
        QLogInfoUtf8(LOG_NAME, "Failed to get monitor file descriptor");
        return false;
    }

    QLogInfoUtf8(LOG_NAME, "USB hotplug monitor initialized successfully");
    return true;
}

void USBHotplugMonitor::start() {
    if (running_.load()) {
        QLogInfoUtf8(LOG_NAME, "Monitor is already running");
        return;
    }

    running_.store(true);
    monitor_thread_ = std::thread(&USBHotplugMonitor::monitorLoop, this);
    QLogInfoUtf8(LOG_NAME, "USB hotplug monitoring started...");

    while (running_.load())
    {
        sleep(2);
    }
    
}

void USBHotplugMonitor::stop() {
    if (!running_.load()) {
        return;
    }

    running_.store(false);
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
    QLogInfoUtf8(LOG_NAME, "USB hotplug monitoring stopped");
}

void USBHotplugMonitor::cleanup() {
    if (monitor_) {
        udev_monitor_unref(monitor_);
        monitor_ = nullptr;
    }
    if (udev_) {
        udev_unref(udev_);
        udev_ = nullptr;
    }
}

void USBHotplugMonitor::monitorLoop() {
    fd_set fds;
    int ret;

    while (running_.load()) {
        FD_ZERO(&fds);
        FD_SET(monitor_fd_, &fds);

        struct timeval timeout;
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        ret = select(monitor_fd_ + 1, &fds, nullptr, nullptr, &timeout);
        
        if (ret > 0 && FD_ISSET(monitor_fd_, &fds)) {
            handleUdevEvent();
        } else if (ret < 0 && errno != EINTR) {
            QLogInfoUtf8(LOG_NAME, "Select error: %s", strerror(errno));
            break;
        }
    }
}

void USBHotplugMonitor::handleUdevEvent() {
    struct udev_device* device = udev_monitor_receive_device(monitor_);
    if (!device) {
        return;
    }

    auto usb_dev_info = usb_dev_manager_.ParseUsbDeviceByInterface(device);
    if (usb_dev_manager_.IsTargetDevice(usb_dev_info))
    {
        kUSBNotifyQueue.Push(usb_dev_info);
    }
    
    udev_device_unref(device);
}

bool USBHotplugMonitor::isUSBStorageDevice(struct udev_device* device) {
    const char* devclass = udev_device_get_sysattr_value(device, "bDeviceClass");
    
    if (devclass && (strcmp(devclass, "08") == 0 || strcmp(devclass, "00") == 0)) {
        struct udev_device* parent = udev_device_get_parent_with_subsystem_devtype(
            device, "usb", "usb_interface");
        
        while (parent) {
            const char* interface_class = udev_device_get_sysattr_value(parent, "bInterfaceClass");
            if (interface_class && strcmp(interface_class, "08") == 0) {
                return true;
            }
            parent = udev_device_get_parent_with_subsystem_devtype(
                parent, "usb", "usb_interface");
        }

        const char* product = udev_device_get_sysattr_value(device, "product");
        if (product) {
            std::string prod_str(product);
            std::transform(prod_str.begin(), prod_str.end(), prod_str.begin(), ::tolower);
            
            if (prod_str.find("storage") != std::string::npos ||
                prod_str.find("disk") != std::string::npos ||
                prod_str.find("flash") != std::string::npos ||
                prod_str.find("drive") != std::string::npos) {
                return true;
            }
        }
    }

    return false;
}

void USBHotplugMonitor::printDeviceInfo(struct udev_device *device)
{
    const char *syspath = udev_device_get_syspath(device);
    const char *driver = udev_device_get_driver(device);
    const char *devnode = udev_device_get_devnode(device);

    QLogInfoUtf8(LOG_NAME, "Device Path: %s", syspath ? syspath : "N/A");
    QLogInfoUtf8(LOG_NAME, "Device: %s", devnode ? devnode : "(no devnode)");
    QLogInfoUtf8(LOG_NAME, "Driver: %s", driver ? driver : "(none)");

    struct udev_device *parent = udev_device_get_parent_with_subsystem_devtype(device, "usb", "usb_device");
    if (parent)
    {
        const char *vendor = udev_device_get_sysattr_value(parent, "idVendor");
        const char *product_id = udev_device_get_sysattr_value(parent, "idProduct");
        const char *manufacturer = udev_device_get_sysattr_value(parent, "manufacturer");
        const char *product = udev_device_get_sysattr_value(parent, "product");
        const char *serial = udev_device_get_sysattr_value(parent, "serial");

        QLogInfoUtf8(LOG_NAME, "=== Parent USB Storage Device Detected ===");
        QLogInfoUtf8(LOG_NAME, "System Path: %s", syspath ? syspath : "N/A");
        QLogInfoUtf8(LOG_NAME, "Vendor ID: %s", vendor ? vendor : "N/A");
        QLogInfoUtf8(LOG_NAME, "Product ID: %s", product_id ? product_id : "N/A");
        QLogInfoUtf8(LOG_NAME, "Manufacturer: %s", manufacturer ? manufacturer : "N/A");
        QLogInfoUtf8(LOG_NAME, "Product: %s", product ? product : "N/A");
        QLogInfoUtf8(LOG_NAME, "Serial: %s", serial ? serial : "N/A");
        QLogInfoUtf8(LOG_NAME, "===================================");
    }
}

// bool USBHotplugMonitor::disableUSBDevice(struct udev_device* device) {
//     const char* syspath = udev_device_get_syspath(device);
//     if (!syspath) {
//         std::cerr << "Failed to get device syspath" << std::endl;
//         return false;
//     }

//     std::string authorized_path = std::string(syspath) + "/authorized";
        
//     std::cout << "Attempting to disable device at: " << authorized_path << std::endl;

//     // 方法1：直接写入 sysfs 文件
//     if (writeToSysfs(authorized_path, "0")) {
//         std::cout << "Successfully disabled USB device via sysfs" << std::endl;
//         return true;
//     }

//     // 方法2：使用系统命令（需要 root 权限）
//     std::string command = "echo 0 > " + authorized_path;
//     if (system(command.c_str()) == 0) {
//         std::cout << "Successfully disabled USB device via system command" << std::endl;
//         return true;
//     }

//     std::cerr << "Failed to disable USB device. Make sure you have root privileges." << std::endl;
//     return false;
// }

// bool USBHotplugMonitor::writeToSysfs(const std::string& path, const std::string& value) {
//     std::ofstream file(path);
//     if (!file.is_open()) {
//         std::cerr << "Failed to open: " << path << std::endl;
//         return false;
//     }

//     file << value;
//     if (file.fail()) {
//         std::cerr << "Failed to write to: " << path << std::endl;
//         return false;
//     }

//     return true;
// }

// 全局信号处理
// std::atomic<bool> g_shutdown(false);

// void signalHandler(int signal) {
//     std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
//     g_shutdown.store(true);
// }

// int main() {
//     // 检查 root 权限
//     if (getuid() != 0) {
//         std::cout << "Warning: This program requires root privileges to disable USB devices." << std::endl;
//         std::cout << "Run with: sudo ./usb_monitor" << std::endl;
//     }

//     // 设置信号处理器
//     signal(SIGINT, signalHandler);
//     signal(SIGTERM, signalHandler);

//     USBHotplugMonitor monitor;

//     if (!monitor.initialize()) {
//         std::cerr << "Failed to initialize USB monitor" << std::endl;
//         return 1;
//     }

//     monitor.start();

//     // 主循环
//     while (!g_shutdown.load()) {
//         std::this_thread::sleep_for(std::chrono::milliseconds(100));
//     }

//     monitor.stop();
//     std::cout << "Program terminated successfully" << std::endl;
//     return 0;
// }

void USBHotplugMonitor::StartMonitor()
{
    initialize();
    start();
}

void USBHotplugMonitor::StopMonitor()
{
    stop();
}

usb_manager::UsbDevice USBHotplugMonitor::GetNotifyUSBDevInfo()
{
    return kUSBNotifyQueue.Pop();
}

bool USBHotplugMonitor::DisableDevice(const usb_manager::UsbDevice& device)
{
    return usb_dev_manager_.DisableDevice(device);
}

bool USBHotplugMonitor::EnableDevice(const usb_manager::UsbDevice& device)
{
    return usb_dev_manager_.EnableDevice(device);
}

int USBHotplugMonitor::DisableTargetDevices()
{
    return usb_dev_manager_.DisableTargetDevices();
}

int USBHotplugMonitor::EnableTargetDevices()
{
    return usb_dev_manager_.EnableTargetDevices();
}
