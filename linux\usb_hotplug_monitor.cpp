#include "usb_hotplug_monitor.h"
#include <iostream>
#include <fstream>
#include <cstring>
#include <unistd.h>
#include <sys/select.h>
#include <libudev.h>
#include <algorithm>
#include <signal.h>
#include "utilset/blocking_queue.hpp"
#include "qlogger.h"
#include "macro_defs.h"

using std::string;

static BlockingQueue<usb_manager::UsbDevice> kUSBNotifyQueue;

USBHotplugMonitor::USBHotplugMonitor() 
    : udev_(nullptr), monitor_(nullptr), monitor_fd_(-1), running_(false), last_usb_dev_{}
{}

USBHotplugMonitor::~USBHotplugMonitor() {
}

bool USBHotplugMonitor::initialize() {
    udev_ = udev_new();
    if (!udev_) {
        QLogInfoUtf8(LOG_NAME, "Failed to create udev context");
        return false;
    }

    monitor_ = udev_monitor_new_from_netlink(udev_, "udev");
    if (!monitor_) {
        QLogInfoUtf8(LOG_NAME, "Failed to create udev monitor");
        return false;
    }

    if (udev_monitor_filter_add_match_subsystem_devtype(monitor_, "usb", "usb_interface") < 0) {
        QLogInfoUtf8(LOG_NAME, "Failed to add subsystem filter");
        return false;
    }

    if (udev_monitor_enable_receiving(monitor_) < 0) {
        QLogInfoUtf8(LOG_NAME, "Failed to enable monitor receiving");
        return false;
    }

    monitor_fd_ = udev_monitor_get_fd(monitor_);
    if (monitor_fd_ < 0) {
        QLogInfoUtf8(LOG_NAME, "Failed to get monitor file descriptor");
        return false;
    }

    QLogInfoUtf8(LOG_NAME, "USB hotplug monitor initialized successfully");
    return true;
}

void USBHotplugMonitor::start() {
    if (running_.load()) {
        QLogInfoUtf8(LOG_NAME, "Monitor is already running");
        return;
    }

    running_.store(true);
    monitor_thread_ = std::thread(&USBHotplugMonitor::monitorLoop, this);
    QLogInfoUtf8(LOG_NAME, "USB hotplug monitoring started...");

    while (running_.load())
    {
        sleep(2);
    }
    
}

void USBHotplugMonitor::stop() {
    if (!running_.load()) {
        return;
    }

    running_.store(false);
    if (monitor_thread_.joinable()) {
        monitor_thread_.join();
    }
}

void USBHotplugMonitor::cleanup() {
    if (monitor_) {
        udev_monitor_unref(monitor_);
        monitor_ = nullptr;
    }
    if (udev_) {
        udev_unref(udev_);
        udev_ = nullptr;
    }
}

void USBHotplugMonitor::monitorLoop() {
    fd_set fds;
    int ret;

    while (running_.load()) {
        FD_ZERO(&fds);
        FD_SET(monitor_fd_, &fds);

        struct timeval timeout;
        timeout.tv_sec = 1;
        timeout.tv_usec = 0;

        ret = select(monitor_fd_ + 1, &fds, nullptr, nullptr, &timeout);
        
        if (ret > 0 && FD_ISSET(monitor_fd_, &fds)) {
            handleUdevEvent();
        } else if (ret < 0 && errno != EINTR) {
            QLogInfoUtf8(LOG_NAME, "Select error: %s", strerror(errno));
            break;
        }
    }
}

void USBHotplugMonitor::handleUdevEvent() {
    struct udev_device* device = udev_monitor_receive_device(monitor_);
    if (!device) {
        return;
    }

    auto usb_dev_info = usb_dev_manager_.ParseUsbDeviceByInterface(device);
    // QLogInfoUtf8(LOG_NAME, "%s", usb_dev_info.ToString().c_str());
    if (usb_dev_manager_.IsTargetDevice(usb_dev_info) && usb_dev_info.action == "add")
    {
        kUSBNotifyQueue.Push(usb_dev_info);
    }
    
    udev_device_unref(device);
}

void USBHotplugMonitor::StartMonitor()
{
    initialize();
    start();
}

void USBHotplugMonitor::StopMonitor()
{
    stop();
    cleanup();
    QLogInfoUtf8(LOG_NAME, "USB hotplug monitoring stopped");
}

usb_manager::UsbDevice USBHotplugMonitor::GetNotifyUSBDevInfo()
{
    return kUSBNotifyQueue.Pop();
}

bool USBHotplugMonitor::DisableDevice(const usb_manager::UsbDevice& device)
{
    if (device.authorized)
    {
        return usb_dev_manager_.DisableDevice(device);
    }
    return true;
}

bool USBHotplugMonitor::EnableDevice(const usb_manager::UsbDevice& device)
{
    if (!device.authorized)
    {
        return usb_dev_manager_.EnableDevice(device);
    }
    return true;
}

int USBHotplugMonitor::DisableTargetDevices()
{
    return usb_dev_manager_.DisableTargetDevices();
}

int USBHotplugMonitor::EnableTargetDevices()
{
    return usb_dev_manager_.EnableTargetDevices();
}

std::vector<usb_manager::UsbDevice> USBHotplugMonitor::EnumTargetDevices()
{
    std::vector<usb_manager::UsbDevice> target_devices{};
    auto devs = usb_dev_manager_.ScanUsbDevices();
    for (auto& dev : devs)
    {
        // QLogInfoUtf8(LOG_NAME, "enum: %s", dev.ToString().c_str());
        bool ret = usb_dev_manager_.IsTargetDevice(dev);
        if (ret)
        {
            target_devices.push_back(dev);
        }
    }
    return target_devices;
}
