// This is a part of the Active Template Library.
// Copyright (C) Microsoft Corporation
// All rights reserved.
//
// This source code is only intended as a supplement to the
// Active Template Library Reference and related
// electronic documentation provided with the library.
// See these sources for detailed information regarding the
// Active Template Library product.

// Used by atlsrv.rc
//
#ifndef ATLSRV_RESID_BASE
#define ATLSRV_RESID_BASE 	0x6000
#endif

#ifndef PERFMON_RESID_BASE
#define PERFMON_RESID_BASE   0x6100
#endif

#ifndef STENCIL_RESID_BASE
#define STENCIL_RESID_BASE   0x6200
#endif

#define IDS_ATLSRV_BAD_REQUEST          (ATLSRV_RESID_BASE+1)
#define IDS_ATLSRV_AUTH_REQUIRED        (ATLSRV_RESID_BASE+2)
#define IDS_ATLSRV_FORBIDDEN            (ATLSRV_RESID_BASE+3)
#define IDS_ATLSRV_NOT_FOUND            (ATLSRV_RESID_BASE+4)
#define IDS_ATLSRV_SERVER_ERROR         (ATLSRV_RESID_BASE+5)
#define IDS_ATLSRV_NOT_IMPLEMENTED      (ATLSRV_RESID_BASE+6)
#define IDS_ATLSRV_BAD_GATEWAY          (ATLSRV_RESID_BASE+7)
#define IDS_ATLSRV_SERVICE_NOT_AVAILABLE (ATLSRV_RESID_BASE+8)
#define IDS_ATLSRV_SERVER_ERROR_BADSRF (ATLSRV_RESID_BASE+9)
#define IDS_ATLSRV_SERVER_ERROR_HNDLFAIL (ATLSRV_RESID_BASE+10)
#define IDS_ATLSRV_SERVER_ERROR_SYSOBJFAIL (ATLSRV_RESID_BASE+11)
#define IDS_ATLSRV_SERVER_ERROR_READFILEFAIL (ATLSRV_RESID_BASE+12)
#define IDS_ATLSRV_SERVER_ERROR_LOADLIB (ATLSRV_RESID_BASE+13)
#define IDS_ATLSRV_SERVER_ERROR_HANDLERIF (ATLSRV_RESID_BASE+14)
#define IDS_ATLSRV_SERVER_ERROR_OUTOFMEM (ATLSRV_RESID_BASE+15)
#define IDS_ATLSRV_SERVER_ERROR_UNEXPECTED	(ATLSRV_RESID_BASE+16)
#define IDS_ATLSRV_SERVER_ERROR_STENCILPARSEFAIL (ATLSRV_RESID_BASE+17)
#define IDS_ATLSRV_SERVER_ERROR_STENCILLOADFAIL (ATLSRV_RESID_BASE+18)
#define IDS_ATLSRV_SERVER_ERROR_HANDLERNOTFOUND (ATLSRV_RESID_BASE+19)
#define IDS_ATLSRV_SERVER_ERROR_BADHANDLERTAG (ATLSRV_RESID_BASE+20)
#define IDS_ATLSRV_SERVER_ERROR_NOHANDLERTAG (ATLSRV_RESID_BASE+21)
#define IDS_ATLSRV_SERVER_ERROR_LONGMETHODNAME (ATLSRV_RESID_BASE+22)
#define IDS_ATLSRV_SERVER_ERROR_LONGHANDLERNAME (ATLSRV_RESID_BASE+23)
#define IDS_ATLSRV_SERVER_ERROR_IMPERSONATIONFAILED (ATLSRV_RESID_BASE+24)
#define IDS_ATLSRV_SERVER_ERROR_ISAPISTARTUPFAILED (ATLSRV_RESID_BASE+25)
#define IDS_ATLSRV_SERVER_ERROR_LOADFILEFAIL (ATLSRV_RESID_BASE+26)
#define IDS_ATLSRV_CRITICAL_LOGMESSAGE (ATLSRV_RESID_BASE+27)
#define IDS_ATLSRV_CRITICAL_HEAPCREATEFAILED (ATLSRV_RESID_BASE+28)
#define IDS_ATLSRV_CRITICAL_WORKERINITFAILED (ATLSRV_RESID_BASE+29)
#define IDS_ATLSRV_CRITICAL_CRITSECINITFAILED (ATLSRV_RESID_BASE+30)
#define IDS_ATLSRV_CRITICAL_THREADPOOLFAILED (ATLSRV_RESID_BASE+31)
#define IDS_ATLSRV_CRITICAL_DLLCACHEFAILED (ATLSRV_RESID_BASE+32)
#define IDS_ATLSRV_CRITICAL_PAGECACHEFAILED (ATLSRV_RESID_BASE+33)
#define IDS_ATLSRV_CRITICAL_STENCILCACHEFAILED (ATLSRV_RESID_BASE+34)
#define IDS_ATLSRV_CRITICAL_SESSIONSTATEFAILED (ATLSRV_RESID_BASE+35)
#define IDS_ATLSRV_CRITICAL_BLOBCACHEFAILED (ATLSRV_RESID_BASE+36)
#define IDS_ATLSRV_CRITICAL_FILECACHEFAILED (ATLSRV_RESID_BASE+37)
#define IDS_ATLSRV_SERVER_ERROR_SOAPNOSOAPACTION (ATLSRV_RESID_BASE+38)

#define IDS_PERFMON_CACHE                       (PERFMON_RESID_BASE+1)
#define IDS_PERFMON_CACHE_HELP                  (PERFMON_RESID_BASE+2)
#define IDS_PERFMON_HITCOUNT                    (PERFMON_RESID_BASE+3)
#define IDS_PERFMON_HITCOUNT_HELP               (PERFMON_RESID_BASE+4)
#define IDS_PERFMON_MISSCOUNT                   (PERFMON_RESID_BASE+5)
#define IDS_PERFMON_MISSCOUNT_HELP              (PERFMON_RESID_BASE+6)
#define IDS_PERFMON_CURRENTALLOCATIONS          (PERFMON_RESID_BASE+7)
#define IDS_PERFMON_CURRENTALLOCATIONS_HELP     (PERFMON_RESID_BASE+8)
#define IDS_PERFMON_MAXALLOCATIONS              (PERFMON_RESID_BASE+9)
#define IDS_PERFMON_MAXALLOCATIONS_HELP         (PERFMON_RESID_BASE+10)
#define IDS_PERFMON_CURRENTENTRIES              (PERFMON_RESID_BASE+11)
#define IDS_PERFMON_CURRENTENTRIES_HELP         (PERFMON_RESID_BASE+12)
#define IDS_PERFMON_MAXENTRIES                  (PERFMON_RESID_BASE+13)
#define IDS_PERFMON_MAXENTRIES_HELP             (PERFMON_RESID_BASE+14)
#define IDS_PERFMON_HITCOUNTRATE                (PERFMON_RESID_BASE+15)
#define IDS_PERFMON_HITCOUNTRATE_HELP           (PERFMON_RESID_BASE+16)

#define IDS_PERFMON_REQUEST						(PERFMON_RESID_BASE+17)
#define IDS_PERFMON_REQUEST_HELP				(PERFMON_RESID_BASE+18)
#define IDS_PERFMON_REQUEST_TOTAL				(PERFMON_RESID_BASE+19)
#define IDS_PERFMON_REQUEST_TOTAL_HELP			(PERFMON_RESID_BASE+20)
#define IDS_PERFMON_REQUEST_FAILED				(PERFMON_RESID_BASE+21)
#define IDS_PERFMON_REQUEST_FAILED_HELP			(PERFMON_RESID_BASE+22)
#define IDS_PERFMON_REQUEST_RATE				(PERFMON_RESID_BASE+23)
#define IDS_PERFMON_REQUEST_RATE_HELP			(PERFMON_RESID_BASE+24)
#define IDS_PERFMON_REQUEST_AVG_RESPONSE_TIME		(PERFMON_RESID_BASE+25)
#define IDS_PERFMON_REQUEST_AVG_RESPONSE_TIME_HELP	(PERFMON_RESID_BASE+26)
#define IDS_PERFMON_REQUEST_CURR_WAITING (PERFMON_RESID_BASE+27)
#define IDS_PERFMON_REQUEST_CURR_WAITING_HELP	(PERFMON_RESID_BASE+28)
#define IDS_PERFMON_REQUEST_MAX_WAITING (PERFMON_RESID_BASE+29)
#define IDS_PERFMON_REQUEST_MAX_WAITING_HELP	(PERFMON_RESID_BASE+30)
#define IDS_PERFMON_REQUEST_ACTIVE_THREADS		(PERFMON_RESID_BASE+31)
#define IDS_PERFMON_REQUEST_ACTIVE_THREADS_HELP	(PERFMON_RESID_BASE+32)


//
// Stencil parse error support
//

// the error stencil
#define IDS_STENCIL_ERROR_STENCIL               (STENCIL_RESID_BASE+1)

// parse errors
#define IDS_STENCIL_UNCLOSEDBLOCK_IF            (STENCIL_RESID_BASE+2)
#define IDS_STENCIL_UNCLOSEDBLOCK_ELSE          (STENCIL_RESID_BASE+3)
#define IDS_STENCIL_UNCLOSEDBLOCK_WHILE         (STENCIL_RESID_BASE+4)
#define IDS_STENCIL_UNOPENEDBLOCK_ENDWHILE      (STENCIL_RESID_BASE+5)
#define IDS_STENCIL_UNOPENEDBLOCK_ELSE          (STENCIL_RESID_BASE+6)
#define IDS_STENCIL_UNOPENEDBLOCK_ENDIF         (STENCIL_RESID_BASE+7)

#define IDS_STENCIL_INVALID_HANDLER             (STENCIL_RESID_BASE+8)
#define IDS_STENCIL_NULLPARAM                   (STENCIL_RESID_BASE+9)
#define IDS_STENCIL_INVALIDSTRING               (STENCIL_RESID_BASE+10)
#define IDS_STENCIL_EMBEDDED_NULL               (STENCIL_RESID_BASE+11)
#define IDS_STENCIL_UNMATCHED_TAG_START         (STENCIL_RESID_BASE+12)
#define IDS_STENCIL_MISMATCHED_TAG_START        (STENCIL_RESID_BASE+13)
#define IDS_STENCIL_BAD_PARAMETER               (STENCIL_RESID_BASE+14)
#define IDS_STENCIL_METHODNAME_TOO_LONG         (STENCIL_RESID_BASE+15)
#define IDS_STENCIL_HANDLERNAME_TOO_LONG        (STENCIL_RESID_BASE+16)
#define IDS_STENCIL_INCLUDE_ERROR               (STENCIL_RESID_BASE+17)
#define IDS_STENCIL_INCLUDE_INVALID             (STENCIL_RESID_BASE+18)
#define IDS_STENCIL_INVALID_SUBHANDLER          (STENCIL_RESID_BASE+19)
#define IDS_STENCIL_UNRESOLVED_REPLACEMENT      (STENCIL_RESID_BASE+20)

// mlang errors
#define IDS_STENCIL_MLANG_COCREATE              (STENCIL_RESID_BASE+21)
#define IDS_STENCIL_MLANG_LCID                  (STENCIL_RESID_BASE+22)
#define IDS_STENCIL_MLANG_GETLOCALE             (STENCIL_RESID_BASE+23)
#define IDS_STENCIL_MLANG_GETCHARSET            (STENCIL_RESID_BASE+24)

// misceallaneous
#define IDS_STENCIL_OUTOFMEMORY                 (STENCIL_RESID_BASE+25)
#define IDS_STENCIL_UNEXPECTED                  (STENCIL_RESID_BASE+26)


// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NO_MFC                     1
#define _APS_NEXT_RESOURCE_VALUE        101
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1000
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif

