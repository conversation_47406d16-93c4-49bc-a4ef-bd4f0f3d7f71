#pragma once
#include <string>
#include "nlohmann/json.hpp"

using json = nlohmann::json;

struct AlertInfo {
    std::string dev_path;
    std::string vendor_id;
    std::string product_id;
    std::string product_name;
    std::string manufacturer;
    std::string serial;
    std::string rule_id = "dev_control_rule_default_001";
    std::string type = "dev_control";
    std::string dev_type;
    int64_t timestamp;
    bool is_enabled;
};

// Implementation of serialization
void to_json(json& j, const AlertInfo& a);

// Implementation of deserialization
void from_json(const json& j, AlertInfo& a);