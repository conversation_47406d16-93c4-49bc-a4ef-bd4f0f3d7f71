/* include/log4cplus/config.h.in.  Generated from configure.in by autoheader.  */

#ifndef LOG4CPLUS_CONFIG_H

#define LOG4CPLUS_CONFIG_H

/* Defined if the compiler supports C99 style variadic macros with
   __VA_ARGS__. */
/* #undef HAS_C99_VARIADIC_MACROS */

/* Defined if the compiler supports GNU style variadic macros. */
/* #undef HAS_GNU_VARIADIC_MACROS */

/* Define to 1 if you have the `clock_gettime' function. */
#undef HAVE_CLOCK_GETTIME

/* Define to 1 if you have the <dlfcn.h> header file. */
#cmakedefine HAVE_DLFCN_H 1

/* Define to 1 if you have the `ftime' function. */
#cmakedefine HAVE_FTIME 1

/* */
#cmakedefine HAVE_GETADDRINFO 1

/* */
#cmakedefine HAVE_GETHOSTBYNAME_R 1

/* Define to 1 if you have the `getpid' function. */
#cmakedefine HAVE_GETPID 1

/* Define to 1 if you have the `gettimeofday' function. */
#cmakedefine HAVE_GETTIMEOFDAY 1

/* Define to 1 if you have the `gmtime_r' function. */
#cmakedefine HAVE_GMTIME_R 1

/* Define to 1 if you have the `htonl' function. */
#cmakedefine HAVE_HTONL 1

/* Define to 1 if you have the `htons' function. */
#cmakedefine HAVE_HTONS 1

/* Define to 1 if you have the `iconv' function. */
#cmakedefine HAVE_ICONV 1

/* Define to 1 if you have the `iconv_close' function. */
#cmakedefine HAVE_ICONV_CLOSE 1

/* Define to 1 if you have the `iconv_open' function. */
#cmakedefine HAVE_ICONV_OPEN 1

/* Define to 1 if you have the <inttypes.h> header file. */
#cmakedefine HAVE_INTTYPES_H 1

/* Define to 1 if you have the `advapi32' library (-ladvapi32). */
#cmakedefine HAVE_LIBADVAPI32 1

/* Define to 1 if you have the `libiconv' function. */
/* #undef HAVE_LIBICONV */

/* Define to 1 if you have the `libiconv_close' function. */
/* #undef HAVE_LIBICONV_CLOSE */

/* Define to 1 if you have the `libiconv_open' function. */
/* #undef HAVE_LIBICONV_OPEN */

/* Define to 1 if you have the `kernel32' library (-lkernel32). */
#cmakedefine HAVE_LIBKERNEL32 1

/* Define to 1 if you have the `nsl' library (-lnsl). */
#cmakedefine HAVE_LIBNSL 1

/* Define to 1 if you have the `rt' library (-lrt). */
#cmakedefine HAVE_LIBRT 1

/* Define to 1 if you have the `socket' library (-lsocket). */
#cmakedefine HAVE_LIBSOCKET 1

/* Define to 1 if you have the `ws2_32' library (-lws2_32). */
#cmakedefine HAVE_LIBWS2_32 1

/* Define to 1 if you have the `localtime_r' function. */
#cmakedefine HAVE_LOCALTIME_R 1

/* Define to 1 if you have the `lstat' function. */
#cmakedefine HAVE_LSTAT 1

/* Define to 1 if you have the <memory.h> header file. */
#cmakedefine HAVE_MEMORY_H 1

/* Define to 1 if you have the `ntohl' function. */
#cmakedefine HAVE_NTOHL 1

/* Define to 1 if you have the `ntohs' function. */
#cmakedefine HAVE_NTOHS 1

/* Define if you have POSIX threads libraries and header files. */
#undef HAVE_PTHREAD

/* Define to 1 if you have the `stat' function. */
#cmakedefine HAVE_STAT 1

/* Define to 1 if you have the <stdint.h> header file. */
#cmakedefine HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#cmakedefine HAVE_STDLIB_H 1

/* Define to 1 if you have the <strings.h> header file. */
#cmakedefine HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#cmakedefine HAVE_STRING_H 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#cmakedefine HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#cmakedefine HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#cmakedefine HAVE_UNISTD_H 1

/* Define to 1 if you have the `vfprintf_s' function. */
#cmakedefine HAVE_VFPRINTF_S 1

/* Define to 1 if you have the `vfwprintf_s' function. */
#cmakedefine HAVE_VFWPRINTF_S 1

/* Define to 1 if you have the `vsnprintf' function. */
#cmakedefine HAVE_VSNPRINTF 1

/* Define to 1 if you have the `vsprintf_s' function. */
#cmakedefine HAVE_VSPRINTF_S 1

/* Define to 1 if you have the `vswprintf_s' function. */
#cmakedefine HAVE_VSWPRINTF_S 1

/* Define to 1 if you have the `_vsnprintf' function. */
#cmakedefine HAVE__VSNPRINTF 1

/* Define to 1 if you have the `_vsnprintf_s' function. */
#cmakedefine HAVE__VSNPRINTF_S 1

/* Define to 1 if you have the `_vsnwprintf_s' function. */
#cmakedefine HAVE__VSNWPRINTF_S 1

/* Defined if the compiler supports __FUNCTION__ macro. */
#cmakedefine HAVE___FUNCTION___MACRO 1

/* Defined if the compiler supports __PRETTY_FUNCTION__ macro. */
#cmakedefine HAVE___PRETTY_FUNCTION___MACRO 1

/* Defined if the compiler provides __sync_add_and_fetch(). */
#cmakedefine HAVE___SYNC_ADD_AND_FETCH 1

/* Defined if the compiler provides __sync_sub_and_fetch(). */
#cmakedefine HAVE___SYNC_SUB_AND_FETCH 1

/* Defined for --enable-debugging builds. */
#undef LOG4CPLUS_DEBUGGING

/* Defined if the compiler understands __declspec(dllimport) or
   __attribute__((visibility("default"))) construct. */
#define LOG4CPLUS_DECLSPEC_EXPORT @LOG4CPLUS_DECLSPEC_EXPORT@

/* Defined if the compiler understands __declspec(dllexport) or construct. */
#define LOG4CPLUS_DECLSPEC_IMPORT @LOG4CPLUS_DECLSPEC_IMPORT@ /**/

/* */
#cmakedefine LOG4CPLUS_HAVE_C99_VARIADIC_MACROS 1

/* */
#cmakedefine LOG4CPLUS_HAVE_CLOCK_GETTIME 1

/* */
#cmakedefine LOG4CPLUS_HAVE_ENAMETOOLONG 1

/* */
#cmakedefine LOG4CPLUS_HAVE_ERRNO_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_FTIME 1

/* */
#define LOG4CPLUS_HAVE_FUNCTION_MACRO 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GETADDRINFO 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GETHOSTBYNAME_R 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GETPID 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GETTID 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GETTIMEOFDAY 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GMTIME_R 1

/* */
#cmakedefine LOG4CPLUS_HAVE_GNU_VARIADIC_MACROS 1

/* */
#cmakedefine LOG4CPLUS_HAVE_HTONL 1

/* */
#cmakedefine LOG4CPLUS_HAVE_HTONS 1

/* */
#cmakedefine LOG4CPLUS_HAVE_ICONV 1

/* */
#cmakedefine LOG4CPLUS_HAVE_ICONV_CLOSE 1

/* */
#cmakedefine LOG4CPLUS_HAVE_ICONV_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_ICONV_OPEN 1

/* */
#cmakedefine LOG4CPLUS_HAVE_LIMITS_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_LOCALTIME_R 1

/* */
#cmakedefine LOG4CPLUS_HAVE_LSTAT 1

/* */
#cmakedefine LOG4CPLUS_HAVE_NETDB_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_NETINET_IN_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_NETINET_TCP_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_NTOHL 1

/* */
#cmakedefine LOG4CPLUS_HAVE_NTOHS 1

/* */
#cmakedefine LOG4CPLUS_HAVE_PRETTY_FUNCTION_MACRO 1

/* */
#cmakedefine LOG4CPLUS_HAVE_STAT 1

/* */
#cmakedefine LOG4CPLUS_HAVE_STDARG_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_STDIO_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_STDLIB_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYSLOG_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYS_SOCKET_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYS_STAT_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYS_SYSCALL_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYS_TIMEB_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYS_TIME_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_SYS_TYPES_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_TIME_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_UNISTD_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE_VFPRINTF_S 1

/* */
#cmakedefine LOG4CPLUS_HAVE_VFWPRINTF_S 1

/* */
#cmakedefine LOG4CPLUS_HAVE_VSNPRINTF 1

/* */
#cmakedefine LOG4CPLUS_HAVE_VSPRINTF_S 1

/* */
#cmakedefine LOG4CPLUS_HAVE_VSWPRINTF_S 1

/* */
#cmakedefine LOG4CPLUS_HAVE_WCHAR_H 1

/* */
#cmakedefine LOG4CPLUS_HAVE__VSNPRINTF 1

/* */
#cmakedefine LOG4CPLUS_HAVE__VSNPRINTF_S 1

/* */
#cmakedefine LOG4CPLUS_HAVE__VSNWPRINTF_S 1

/* */
#cmakedefine LOG4CPLUS_HAVE___SYNC_ADD_AND_FETCH 1

/* */
#cmakedefine LOG4CPLUS_HAVE___SYNC_SUB_AND_FETCH 1

/* Define if this is a single-threaded library. */
#undef LOG4CPLUS_SINGLE_THREADED

/* */
#undef LOG4CPLUS_USE_PTHREADS

/* Define for compilers/standard libraries that support more than just the "C"
   locale. */
#undef LOG4CPLUS_WORKING_LOCALE

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
#undef PTHREAD_CREATE_JOINABLE

/* Define to 1 if you have the ANSI C header files. Seems to be unused*/
#cmakedefine STDC_HEADERS 1

/* Define to int if undefined. */
#cmakedefine socklen_t int

#endif // LOG4CPLUS_CONFIG_H
