#include "cdrom_detector.h"

#include <cstring>
#include <cctype>

struct CdromDevice {
    unsigned int vendor_id;
    unsigned int product_id;
};

struct CdromManufacturer {
    unsigned int vendor_id;
    const char* names[5];
};

// 已知光驱设备
static const CdromDevice kKnownCdromDevices[] = {
    // HLDS
    {0x152e, 0x2571}, {0x152e, 0x2572}, {0x152e, 0x2573},
    // LG
    {0x043e, 0x42bd}, {0x043e, 0x70f1},
    // ASUS
    {0x0b05, 0x7774}, {0x0b05, 0x7780},
    // Samsung
    {0x04e8, 0x685b}, {0x04e8, 0x685c},
    // Sony
    {0x054c, 0x05c8}, {0x054c, 0x02f2},
    // Pioneer
    {0x05e3, 0x0701}, {0x05e3, 0x0702},
    // HP
    {0x03f0, 0x4117}, {0x03f0, 0x4217},
    // Dell
    {0x413c, 0x5534},
    // Lite-On
    {0x13fd, 0x0840}, {0x13fd, 0x3940},
    // Buffalo
    {0x0411, 0x012e},
    // I-O DATA
    {0x04bb, 0x010f},
    {0, 0}
};

// 光驱厂商
static const CdromManufacturer kCdromManufacturers[] = {
    {0x152e, {"HLDS", "HITACHI-LG", "HL-DT-ST", "HITACHI", nullptr}},
    {0x043e, {"LG", "LG ELECTRONICS", "LGE", nullptr}},
    {0x0b05, {"ASUS", "ASUSTEK", nullptr}},
    {0x04e8, {"SAMSUNG", nullptr}},
    {0x054c, {"SONY", nullptr}},
    {0x05e3, {"PIONEER", nullptr}},
    {0x03f0, {"HP", "HEWLETT-PACKARD", nullptr}},
    {0x413c, {"DELL", nullptr}},
    {0x13fd, {"LITE-ON", "LITEON", nullptr}},
    {0x0411, {"BUFFALO", nullptr}},
    {0x04bb, {"I-O DATA", "IODATA", nullptr}},
    {0, {nullptr}}
};

// 光驱关键词
static const char* kCdromKeywords[] = {
    "DVD", "CD", "CDROM", "CD-ROM", "CD-RW", "DVD-ROM", "DVD-RW", "DVD+RW",
    "OPTICAL", "WRITER", "RECORDER", "BURNER", "MULTI", "COMBO",
    "BLU-RAY", "BLURAY", "BD", "BD-ROM", "BD-R", "BD-RE",
    "SUPER MULTI", "MULTI DRIVE", "OPTICAL DRIVE", "DISC DRIVE",
    "DVD WRITER", "CD WRITER", "DVD RECORDER", "PORTABLE DRIVE",
    nullptr
};

static void StringToUpper(char* str) {
    if (!str) return;
    for (int i = 0; str[i]; i++) {
        str[i] = std::toupper(static_cast<unsigned char>(str[i]));
    }
}

static void SafeStringCopyUpper(char* dest, const char* src, size_t max_len) {
    if (!dest || !src || max_len == 0) return;
    strncpy(dest, src, max_len - 1);
    dest[max_len - 1] = '\0';
    StringToUpper(dest);
}

static bool CheckExactMatch(unsigned int vendor_id, unsigned int product_id) {
    for (int i = 0; kKnownCdromDevices[i].vendor_id != 0; i++) {
        if (kKnownCdromDevices[i].vendor_id == vendor_id && 
            kKnownCdromDevices[i].product_id == product_id) {
            return true;
        }
    }
    return false;
}

static bool CheckCdromVendor(unsigned int vendor_id, const char* manufacturer) {
    // 检查厂商ID
    for (int i = 0; kCdromManufacturers[i].vendor_id != 0; i++) {
        if (kCdromManufacturers[i].vendor_id == vendor_id) {
            return true;
        }
    }
    
    // 检查厂商名称
    if (manufacturer) {
        char mfg_upper[256];
        SafeStringCopyUpper(mfg_upper, manufacturer, sizeof(mfg_upper));
        
        for (int i = 0; kCdromManufacturers[i].vendor_id != 0; i++) {
            for (int j = 0; kCdromManufacturers[i].names[j]; j++) {
                if (strstr(mfg_upper, kCdromManufacturers[i].names[j])) {
                    return true;
                }
            }
        }
    }
    
    return false;
}

static bool CheckProductKeywords(const char* product_name) {
    if (!product_name) return false;
    
    char product_upper[512];
    SafeStringCopyUpper(product_upper, product_name, sizeof(product_upper));
    
    for (int i = 0; kCdromKeywords[i]; i++) {
        if (strstr(product_upper, kCdromKeywords[i])) {
            return true;
        }
    }
    
    return false;
}

/**
 * 判断设备是否为光驱
 * @param vendor_id 厂商ID
 * @param product_id 产品ID
 * @param product_name 产品名称
 * @param manufacturer 制造商
 * @return true: 是光驱, false: 不是光驱
 */
bool IsCdromDevice(unsigned int vendor_id, unsigned int product_id,
                   const char* product_name, const char* manufacturer) {
    // 精确匹配
    if (CheckExactMatch(vendor_id, product_id)) {
        return true;
    }
    
    // 启发式检测
    int score = 0;
    
    // 已知光驱厂商 (+30分)
    if (CheckCdromVendor(vendor_id, manufacturer)) {
        score += 30;
    }
    
    // 产品名称包含光驱关键词 (+40分)
    if (CheckProductKeywords(product_name)) {
        score += 40;
    }
    
    // 制造商名称包含存储相关词汇 (+20分)
    if (manufacturer) {
        char mfg_upper[256];
        SafeStringCopyUpper(mfg_upper, manufacturer, sizeof(mfg_upper));
        
        if (strstr(mfg_upper, "DATA STORAGE") || strstr(mfg_upper, "OPTICAL") ||
            strstr(mfg_upper, "DISC") || strstr(mfg_upper, "MEDIA")) {
            score += 20;
        }
    }
    
    // HLDS光驱产品ID范围 (+10分)
    if (vendor_id == 0x152e && (product_id >= 0x2570 && product_id <= 0x2580)) {
        score += 10;
    }
    
    return score >= 70;
}