#include "network_helper.h"

#if defined(__linux__) || defined(__APPLE__) || defined(__FreeBSD__)
#include <arpa/inet.h>
#include <cstring>
#include <iostream>
#include <linux/if.h>
#include <netdb.h>
#include <netinet/in.h>
#include <string>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <unistd.h>

using std::string;
static inline bool getRouteIP(string& localIP)
{
	int sock = socket(PF_INET, SOCK_DGRAM, 0);
	sockaddr_in loopback;

	if (sock == -1) {
		std::cerr << "Could not socket\n";
		return false;
	}

	std::memset(&loopback, 0, sizeof(loopback));
	loopback.sin_family = AF_INET;
	loopback.sin_addr.s_addr = 1337;   // can be any IP address
	loopback.sin_port = htons(9);      // using debug port

	if (connect(sock, reinterpret_cast<sockaddr*>(&loopback), sizeof(loopback)) == -1) {
		close(sock);
		std::cerr << "Could not connect\n";
		return false;
	}

	socklen_t addrlen = sizeof(loopback);
	if (getsockname(sock, reinterpret_cast<sockaddr*>(&loopback), &addrlen) == -1) {
		close(sock);
		std::cerr << "Could not getsockname\n";
		return false;
	}

	close(sock);

	char buf[INET_ADDRSTRLEN];
	if (inet_ntop(AF_INET, &loopback.sin_addr, buf, INET_ADDRSTRLEN) == 0x0) {
		std::cerr << "Could not inet_ntop\n";
		return false;
	}
	else {
		// std::cout << "Local ip address: " << buf << "\n";
		localIP = buf;
	}

	return true;
}

static inline std::string GetMAC(const std::string& local_ip) {

	int sockfd;
	struct ifreq* ifreqbuf, * ifreqptr;
	struct ifconf ifc;
	char buf[1024];

	// Create socket
	sockfd = socket(AF_INET, SOCK_DGRAM, 0);
	if (sockfd == -1) {
		return "";
	}

	// Get network interface list
	ifc.ifc_len = sizeof(buf);
	ifc.ifc_buf = buf;

	if (ioctl(sockfd, SIOCGIFCONF, &ifc) == -1) {
		close(sockfd);
		return "";
	}

	// Traverse network interface information
	int count = ifc.ifc_len / sizeof(struct ifreq);
	for (ifreqbuf = ifc.ifc_req; count > 0; ++ifreqbuf, --count)
	{
		// Get network interface name
		char name[IFNAMSIZ] = { 0 };
		strncpy(name, ifreqbuf->ifr_name, IFNAMSIZ);

		// Get IP address
		struct sockaddr_in* sinptr = (struct sockaddr_in*)&(ifreqbuf->ifr_addr);
		char szIpAddr[INET_ADDRSTRLEN] = { 0 };
		inet_ntop(AF_INET, &(sinptr->sin_addr), szIpAddr, INET_ADDRSTRLEN);

		if (strcmp(szIpAddr, local_ip.c_str()) == 0) {
			// Get MAC address
			struct ifreq ifreq;
			memset(&ifreq, 0, sizeof(ifreq));
			strcpy(ifreq.ifr_name, name);

			if (ioctl(sockfd, SIOCGIFHWADDR, &ifreq) == -1) {
				continue;
			}

			char mac_val[20] = { 0 };
			unsigned char* mac = (unsigned char*)ifreq.ifr_hwaddr.sa_data;
			sprintf(mac_val, "%02x:%02x:%02x:%02x:%02x:%02x", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

			close(sockfd);
			return mac_val;
		}
	}

	close(sockfd);
	return "";
}

#elif defined(_WIN32)

#include <winsock2.h>
#include <iphlpapi.h>
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <iostream>

#pragma warning(disable:4996)

// Link with Iphlpapi.lib
#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")

#define WORKING_BUFFER_SIZE 15000
#define MAX_TRIES 3

#define MALLOC(x) HeapAlloc(GetProcessHeap(), 0, (x))
#define FREE(x) HeapFree(GetProcessHeap(), 0, (x))

using namespace std;
static inline bool getRouteIP(string& localIP)
{
	DWORD destIP = inet_addr("*********");
	PMIB_IPFORWARDROW pBest = new MIB_IPFORWARDROW{};
	auto ret = GetBestRoute(destIP, 0, pBest);
	if (ret != NO_ERROR)
		return false;

	auto ifIndex = pBest->dwForwardIfIndex;
	delete pBest;
	pBest = nullptr;

	/* Declare and initialize variables */
	DWORD dwSize = 0;
	DWORD dwRetVal = 0;

	unsigned int i = 0;

	// Set the flags to pass to GetAdaptersAddresses
	ULONG flags = GAA_FLAG_INCLUDE_PREFIX;

	// default to unspecified address family (both)
	ULONG family = AF_INET;

	LPVOID lpMsgBuf = NULL;

	PIP_ADAPTER_ADDRESSES pAddresses = NULL;
	ULONG outBufLen = 0;
	ULONG Iterations = 0;

	PIP_ADAPTER_ADDRESSES pCurrAddresses = NULL;
	PIP_ADAPTER_UNICAST_ADDRESS pUnicast = NULL;
	PIP_ADAPTER_ANYCAST_ADDRESS pAnycast = NULL;
	PIP_ADAPTER_MULTICAST_ADDRESS pMulticast = NULL;
	IP_ADAPTER_DNS_SERVER_ADDRESS* pDnServer = NULL;
	IP_ADAPTER_PREFIX* pPrefix = NULL;

	// Allocate a 15 KB buffer to start with.
	outBufLen = WORKING_BUFFER_SIZE;
	do {
		pAddresses = (IP_ADAPTER_ADDRESSES*)MALLOC(outBufLen);
		if (pAddresses == NULL) {
			printf("Memory allocation failed for IP_ADAPTER_ADDRESSES struct\n");
			return false;
		}

		dwRetVal = GetAdaptersAddresses(family, flags, NULL, pAddresses, &outBufLen);

		if (dwRetVal == ERROR_BUFFER_OVERFLOW) {
			FREE(pAddresses);
			pAddresses = NULL;
		}
		else {
			break;
		}

		Iterations++;

	} while ((dwRetVal == ERROR_BUFFER_OVERFLOW) && (Iterations < MAX_TRIES));

	if (dwRetVal != NO_ERROR)
	{
		return false;
	}

	// If successful, output some information from the data we received
	pCurrAddresses = pAddresses;
	while (pCurrAddresses) {
		if (ifIndex == pCurrAddresses->IfIndex)
		{
			pUnicast = pCurrAddresses->FirstUnicastAddress;
			if (pUnicast != NULL) {
				SOCKADDR_IN* ipv4 = reinterpret_cast<SOCKADDR_IN*>(pUnicast->Address.lpSockaddr);
				localIP = inet_ntoa(ipv4->sin_addr);
				break;
			}
		}
		pCurrAddresses = pCurrAddresses->Next;
	}

	if (pAddresses)
		FREE(pAddresses);

	if (localIP.empty())
		return false;

	return true;
}

static inline std::string GetMAC(std::string local_ip)
{
	std::string mac_str;
	unsigned char mac[6];
	ULONG MacLen = 6;
	ULONG DestIP = inet_addr(local_ip.c_str());
	int rs = SendARP(DestIP, (ULONG)NULL, (PULONG)mac, (PULONG)&MacLen);
	if (rs == 0)
	{
		char buf[32];
		sprintf(buf, "%02X-%02X-%02X-%02X-%02X-%02X",
			(unsigned int)mac[0],
			(unsigned int)mac[1],
			(unsigned int)mac[2],
			(unsigned int)mac[3],
			(unsigned int)mac[4],
			(unsigned int)mac[5]);
		mac_str = buf;
	}
	return mac_str;
}
#endif

NetHelper::NetHelper()
{
}

NetHelper::~NetHelper()
{
}

string NetHelper::GetValidIP()
{
	string temp_ip;
	getRouteIP(temp_ip);
	return temp_ip;
}

string NetHelper::GetMacByIP(const string& ip)
{
	return GetMAC(ip);
}

const string& NetHelper::GetHostMacAddr()
{
	if (!host_mac_.empty())
		return host_mac_;

	host_mac_ = NetHelper::GetMacByIP(NetHelper::GetValidIP());
	if (host_mac_.empty())
	{
		host_mac_ = "null";
	}

	return host_mac_;
}
