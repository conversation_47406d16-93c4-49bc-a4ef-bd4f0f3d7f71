#ifndef _UTIL_SOCK_HPP_
#define _UTIL_SOCK_HPP_

#include <util/core.hpp>

#ifndef NET_PORT
typedef unsigned short	NET_PORT;
#endif 

#ifndef INVALID_NET_PORT
#define INVALID_NET_PORT 0
#endif 

#ifndef NET_ADDR
typedef const char*		NET_ADDR;
#endif 

#ifndef NET_ADDR_INT
typedef unsigned int	NET_ADDR_INT;
#endif 


#ifndef BUFFER_PTR
typedef unsigned char*	BUFFER_PTR;
#endif

#ifndef BUFFER_SIZE
typedef unsigned long	BUFFER_SIZE;
#endif


/*
<PERSON><PERSON> converts unsigned short types from host order to network order
Htonl converts unsigned long type from host order to network order
NTOHS converts unsigned short types from network order to host order
NTOHL converts unsigned long types from network order to host order
*/


#if (TARGET_OS == OS_WINDOWS)
    typedef SOCKET              _sock_t;
    #ifndef socklen_t
		typedef int				          socklen_t;
    #endif

	#define SHUT_SOCK_RD	SD_RECEIVE
	#define SHUT_SOCK_RW	SD_SEND
	#define SHUT_SOCK_BOTN	SD_BOTH

#elif (TARGET_OS == OS_POSIX)
    #define INVALID_SOCKET      -1
    typedef int                 _sock_t;

	#define SHUT_SOCK_RD	SHUT_RD
	#define SHUT_SOCK_RW	SHUT_WR
	#define SHUT_SOCK_BOTN	SHUT_RDWR

#elif (TARGET_OS == OS_DARWIN)

	#define INVALID_SOCKET      -1
	typedef int                 _sock_t;

	#define SHUT_SOCK_RD	SHUT_RD
	#define SHUT_SOCK_RW	SHUT_WR
	#define SHUT_SOCK_BOTN	SHUT_RDWR
#endif

#endif
