#ifndef _UTILEX__LOCKQUEUE_H_
#define _UTILEX__LOCKQUEUE_H_

#include <utilex/util.h>

template<class _Ty, class _Tr = default_sentry>
class CLockQueue :	protected std::queue<_Ty>,
					public CAutoLock
{
	typedef std::queue<_Ty> _base;
public:
	CLockQueue() {

	}
	~CLockQueue() {

	}
public:
	std_method(push_queue)(_Ty pNode) {

		rc_assert(pNode != NULL, E_FAIL);

		SYNC_OBJ(this);
		this->_base::push(pNode);

		return S_OK;
	}

	std_method(pop_queue)(_Ty* pNode) {

		SYNC_OBJ(this);
		*pNode = this->_base::front();
		this->_base::pop();
		return S_OK;
	}

	std_method(get_front)(_Ty* pNode) {

		SYNC_OBJ(this);
		*pNode = this->_base::front();
		return S_OK;
	}

	std_method(get_back)(_Ty* pNode) {

		SYNC_OBJ(this);
		*pNode = this->_base::back();
		return S_OK;
	}

	std_method_(UINT, size)() {

		SYNC_OBJ(this);
		return this->_base::size();
	}

	std_method(auto_clear)() {

		SYNC_OBJ(this);
		UINT uloop =  this->_base::size();
		while (uloop != 0)
		{
			_Ty pNode = this->_base::front();
			this->_base::pop();
			m_tr.destroy(pNode);
			uloop = this->_base::size();
		}
		
		return S_SUCCESS;
	}
private:
	_Tr m_tr;
};
#endif



