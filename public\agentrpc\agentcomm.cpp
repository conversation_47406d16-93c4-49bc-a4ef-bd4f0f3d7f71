#ifdef _WIN32

#pragma warning( disable : 4290 )
#else 

#include <unistd.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>

#endif

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <sys/types.h>

#include <errno.h>
#include "agentcomm.h"

bool g_run = false;

AgentPlugin g_plugin;

int comm_init()
{
	int ret = -1;

	g_plugin.Init("donghaitest", "1.0", "127.0.0.1", 30123, 61117, onmsg);
	
	return 0;
}


int comm_unint()
{
	return 0;
}


int onmsg(string& strtag, string& strmsg)
{
	int ret = 0; 

	printf("onmsg: %s , %s\n", strtag.c_str(), strmsg.c_str());

	return ret;
}


int report(const char* strrestype, const char* strdata, int len)
{
	return g_plugin.Report(strrestype, strdata, len);
}
