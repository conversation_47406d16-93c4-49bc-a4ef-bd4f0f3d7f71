#ifndef _COMPAT_FILE_H_
#define _COMPAT_FILE_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C"
{
#endif

_fd_t _file_open(const basic_tchar *filepath, int flags, int mode);
int _file_close(_fd_t fh);
_fd_size _file_lseek(_fd_t fh, _off_t offset, int whence);
int _file_read(_fd_t fh, void *buf, size_t size);
int _file_write(_fd_t fh, const void *buf, size_t size);
int64_t _file_fsize(_fd_t fh);


#ifdef	__cplusplus
}
#endif


#endif


