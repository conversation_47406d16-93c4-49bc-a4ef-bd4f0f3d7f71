#pragma once
#include <string>
#include "../commproxybase.h"
#include "IAgentProxyDef.h"

struct IAgentProxy : public ICommBase
{
	virtual int Register(const char* pluginName, int pluginPort, IAgentProxyMsgCall* callback) = 0;
	virtual void UnRegister(int clientID, IAgentProxyMsgCall* callback) = 0;
	virtual bool Report(int clientID, const char* resType, const char* data, int len)=0;
};

_DEF_IID(IAgentProxy, "{1CB19D51-376D-43C8-BD4B-1DB85E36EC8B}", 0x1CB19D51, 0x376D, 0x43C8, 0xBD, 0x4B, 0x1D, 0xB8, 0x5E, 0x36, 0xEC, 0x8B);

struct IAgentProxyCtl : public ICommBase
{
	virtual int Control(int clientID, const char* name, const char* data, int len) = 0;
};

_DEF_IID(IAgentProxyCtl, "{9516B8A1-7CCD-4EEC-AC8F-4C5DFBAED66F}", 0x9516b8a1, 0x7ccd, 0x4eec, 0xac, 0x8f, 0x4c, 0x5d, 0xfb, 0xae, 0xd6, 0x6f);

// {9C990E65-F33F-491F-8C59-5A96C5F16B0D}
_DEF_GUID(CLSID_AgentProxy,
	0x9c990e65, 0xf33f, 0x491f, 0x8c, 0x59, 0x5a, 0x96, 0xc5, 0xf1, 0x6b, 0xd );
