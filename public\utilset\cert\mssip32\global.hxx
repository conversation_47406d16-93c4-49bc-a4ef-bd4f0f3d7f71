//+-------------------------------------------------------------------------
//
//  Microsoft Windows
//
//  Copyright (C) Microsoft Corporation, 1996 - 1999
//
//  File:       global.hxx
//
//  History:    17-Feb-97   p<PERSON><PERSON>    created
//
//--------------------------------------------------------------------------

#define STRICT
#define NO_ANSIUNI_ONLY
#define CRYPT_OID_INFO_HAS_EXTRA_FIELDS

#include    <windows.h>
#include    <assert.h>
#include    <ole2.h>
#include    <regstr.h>
#include    <wincrypt.h>
#include    <string.h>
#include    <malloc.h>
#include    <memory.h>
#include    <stdlib.h>
#include    <stddef.h>
#include    <stdio.h>
#include    <wchar.h>
#include    <tchar.h>
#include    <time.h>
#include    <shellapi.h>
#include    <imagehlp.h>
#include    <prsht.h>
#include    <commctrl.h>
#include    <wininet.h>
//#include    <dbgdef.h>

//#include    "cryptreg.h"
#include    "wintrust.h"
//#include    "wintrustp.h"
#include    "softpub.h"
//#include    "unicode.h"

//#include    "crtem.h"
#include    "sipbase.h"
#include    "mssip.h"
#include    "mscat.h"

#include    "mssip32.h"
//#include    "sipguids.h"

#include    "../common/gendefs.h"

#define     DBG_SS                          DBG_SS_SIP

#pragma hdrstop

#define TRACE_ERROR_EX(x, label) \
label: \
	goto ErrorReturn;

#define SET_ERROR_VAR_EX(x, label, err) \
	label: \
		SetLastError(err); \
		goto ErrorReturn;


#ifndef DELETE_OBJECT
#define DELETE_OBJECT(obj0) if (obj0) { delete obj0; obj0 = NULL; }
#endif

#define WVT_MODID_MSSIP 0 //Ϲд