#ifndef _UTILEX_UTIL_H_
#define _UTILEX_UTIL_H_

#include <util/util.h>
#include <utilex/crt.hpp>

#ifdef __cplusplus

#include <utilex/alloc.hpp>
#include <utilex/plus.hpp>
#include <utilex/autolock.hpp>
#include <utilex/sentry.hpp>
#include <utilex/sem.hpp>
#include <utilex/thread.hpp>
#include <utilex/threadgroup.hpp>
#include <utilex/semthread.hpp>
#include <utilex/lockqueue.hpp>
#include <utilex/locklist.hpp>
#endif

#endif
