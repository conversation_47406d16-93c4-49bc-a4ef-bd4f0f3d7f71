#ifndef _COMPAT_ARRAY_H_
#define _COMPAT_ARRAY_H_

#include <compat/define.h>
#include <compat/error.h>
#include <compat/stdlib/iterator.h>

#ifdef  __cplusplus
extern "C" 
{
#endif

typedef	struct array_s array_t;

struct array_s {
	int     capacity;
	int     count;		
	void    **items;	

	int		(*push_back)(struct array_s*, void*);
	int		(*push_front)(struct array_s*, void*);

	int		(*array_pred_insert)(array_t *a, int position, void *obj);
	int		(*array_succ_insert)(array_t *a, int position, void *obj);

	void*	(*pop_back)(struct array_s*);
	void*	(*pop_front)(struct array_s*);

	int		(*array_delete_idx)(array_t *a, int position, void(*free_fn)(void *));
	int		(*array_delete)(array_t *a, int position, void(*free_fn)(void*));
	int		(*array_delete_range)(array_t *a, int ibegin, int iend, void(*free_fn)(void*));
	int		(*array_mv_idx)(array_t *a, int ito, int ifrom, void(*free_fn)(void *));

	void*	(*iter_head)(ITER*, struct array_s*);
	void*	(*iter_next)(ITER*, struct array_s*);
	void*	(*iter_tail)(ITER*, struct array_s*);
	void*	(*iter_prev)(ITER*, struct array_s*);

	void	(*array_pre_append)(array_t *a, int app_count);
	void*	(*array_index)(const array_t *a, int idx);
	int		(*array_size)(const array_t *a);
};



int		array_init(array_t* a,int init_size);
void	array_clean(array_t *a, void (*free_fn)(void *));


#ifdef  __cplusplus
}
#endif

#endif

