#ifndef baseptr_h__
#define baseptr_h__

#pragma once
namespace atsdk{
	namespace util{
		class baseptr
		{
		public:
			baseptr(void):ptr_m_refCount(0){} 
			virtual ~baseptr(void){}
			ULONG ptr_AddRef(){
				return InterlockedIncrement(&ptr_m_refCount);
			}
			ULONG ptr_Release(){
				ULONG lret = InterlockedDecrement(&ptr_m_refCount);
				if(lret) return lret;
				delete this;
				return lret;
			}
		private:
			volatile LONG ptr_m_refCount;
		};

		template<typename T>
		class autodelPtr{
		public:
			autodelPtr():_pPtr(NULL){}
			autodelPtr(T * src):_pPtr(NULL){
				ATLASSERT(src);
				if(!src) return;
				src->ptr_AddRef();
				_pPtr = src;
			}
			autodelPtr &operator=(T * src){
				ATLASSERT(src);
				if(!src) return *this;
				src->ptr_AddRef();
				_pPtr = src;
				return *this;
			}
			autodelPtr(autodelPtr & src):_pPtr(NULL){
				ATLASSERT(src._pPtr);
				if(!src._pPtr) return ;

				src._pPtr->ptr_AddRef();
				_pPtr = src._pPtr;
			}
			autodelPtr &operator=(autodelPtr & src){
				ATLASSERT(src._pPtr);
				if(!src._pPtr) return *this;

				src._pPtr->ptr_AddRef();
				_pPtr = src._pPtr;
				return *this;
			}
			virtual ~autodelPtr(){
				ATLASSERT(_pPtr);
				if(!_pPtr) return ;
				_pPtr->ptr_Release();
				_pPtr = NULL;
			}
			void release(){
				*this = NULL;
			}
		private:
			baseptr * _pPtr;
		public:

			operator T*(){
				return static_cast<T*>(_pPtr);
			}
			T* operator ->(){
				return static_cast<T*>(_pPtr);
			}
			operator BOOL (){
				return !!_pPtr;
			}
			BOOL operator !(){
				return !_pPtr;
			}

		public:

		};

	};
};
#endif // baseptr_h__