#pragma once
#include "commproxybase.h"
#include "procsmgr_i/IAgentProxy.h"
#include <mutex>
#include <queue>
#include <thread>
#include "nlohmann/json.hpp"
#include "acsmon/dispcenter.h"
#include "acsmon/ukif.h"
#include "acsmon/msgpack.h"
#if defined(WINDOWS_COMPILE)
#include <unknwn.h>
#else
#include <condition_variable>
#endif

#include "utilset/agent_cfg_parser.h"
#include "usbprotector.h"
#include "utilset/blocking_queue.hpp"
#include "common_types.h"

static GUID USBProtector_GUID =
{ 0x1095278d, 0x2161, 0x46af, { 0x8f, 0xb9, 0x2a, 0xd, 0x23, 0x58, 0x32, 0x3c } };

using json = nlohmann::json;

class PluginRegister :
    public CommProxyBase<PluginRegister>,
    public IAgentProxyMsgCall

{
public:
    PluginRegister(void);
    virtual ~PluginRegister(void);
    #if defined(WINDOWS_COMPILE)
    STD_QI(IAtPlugin, IAtPluginRun);
    #else
    STD_QI();
    #endif
    virtual HRESULT OnAfterInit();
    virtual HRESULT OnBeforeUninit();
    virtual HRESULT OnAfterStart();
    virtual HRESULT OnBeforeStop();

    virtual int AgentProxyMsgCall(const char* tag, const char* msg);
    SComPtr<IAgentProxy> sp_agent_;
    // SComPtr<IMsgDispCenter> m_pIdispcenter;
    // SComPtr<IMsgPack> m_pIMsgPack;

private:
    void HandleTaskOrPolicy();
    void ReportMsg();

    BlockingQueue<AgentMsg> agent_msg_queue_;
    BlockingQueue<string> report_msg_queue_;

    int client_id_;
    std::atomic_bool run_flag_;
    USBProtector usbprotector_;

    std::vector<shared_ptr<std::thread>> threads_;
};

// {1095278D-2161-46AF-8FB9-2A0D2358323C}
_DEF_GUID(CLSID_USBProtector_Plugin,
    0x1095278d, 0x2161, 0x46af, 0x8f, 0xb9, 0x2a, 0xd, 0x23, 0x58, 0x32, 0x3c);
