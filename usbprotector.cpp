#include "usbprotector.h"
#include <thread>
#include <memory>
#include <vector>
#include "qlogger.h"
#include "utility.h"
#include "global_defs.h"
#include "macro_defs.h"
#include "policy_parser.h"
#include "utilset/string_conversion.hpp"

#if defined(_WIN32)
#include "devcon/cmds.h"

#else

#endif

using std::vector;

USBProtector::USBProtector(BlockingQueue<string> &report_msg_queue) : report_msg_queue_(report_msg_queue)
{
    run_flag_ = true;
}

USBProtector::~USBProtector()
{
}

void USBProtector::HandleAgentProxyMsg(const string &tag, const string &msg)
{
    if (0 == strcmp(tag.c_str(), "Plugin.Policy"))
    {
        ParsePolicy(msg);
    }
}

void USBProtector::Start()
{
    /*
        1、读取上一次策略，根据策略停止当前所有 u 盘设备或启用所有 u 盘设备。如果要精确管控，那么数据就要落盘。
        2、开启 u 盘监控，在监控中，如总开关为 false 则不工作，此外根据策略禁用插入的 u 盘，或者忽略。
        3、当有新策略到来，如果要启用所有 u 盘则开启，禁用则停止所有。
    */

    ParseLocalPolicy();

#if defined(_WIN32)
    std::thread t(&USBManager::StartMonitor, &usb_manager_);
    t.detach();

    while (run_flag_)
    {
        auto usb_dev_id = usb_manager_.GetNotifyUSBDevId();
        if (!usb_dev_id.empty())
        {
            auto dev_info = usb_device::GetDeviceInfoByInstanceId(usb_dev_id);
            std::lock_guard<std::mutex> lg(curr_policy_mtx_);
            if (curr_policy_.isOpen)
            {
                auto alert_info = GenAlertInfo(dev_info, curr_policy_, true);
                json alert_info_json = alert_info;
                report_msg_queue_.Push(alert_info_json.dump(-1, ' '));
            }
        }
    }
#else
    std::thread t(&USBHotplugMonitor::StartMonitor, &usb_manager_);
    t.detach();

    while (run_flag_)
    {
        auto usb_dev = usb_manager_.GetNotifyUSBDevInfo();
        std::lock_guard<std::mutex> lg(curr_policy_mtx_);
        if (curr_policy_.isOpen)
        {
            auto alert_info = GenAlertInfo(usb_dev, curr_policy_);
            json alert_info_json = alert_info;
            report_msg_queue_.Push(alert_info_json.dump(-1, ' '));
        }
    }
#endif
}

void USBProtector::Stop()
{
    run_flag_ = false;
#if defined(_WIN32)
    usb_manager_.StopMonitor();
#else
    usb_manager_.StopMonitor();
#endif
}

void USBProtector::EnableAllUSBStorage()
{
#if defined(_WIN32)
    vector<PTSTR> argv = {L"=DiskDrive", L"USBSTOR\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=WPD", L"USB\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=CDROM", L"USBSTOR\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=Net", L"USB\\*", nullptr};
    cmdEnable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());
#else
    usb_manager_.EnableTargetDevices();
#endif
}

void USBProtector::DisableAllUSBStorage()
{
#if defined(_WIN32)
    vector<PTSTR> argv = {L"=DiskDrive", L"USBSTOR\\*", nullptr}; // usb 存储设备
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=WPD", L"USB\\*", nullptr}; // 安卓手机 usb 数据连接
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=CDROM", L"USBSTOR\\*", nullptr};
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());

    argv = {L"=Net", L"USB\\*", nullptr}; // usb 网卡
    cmdDisable(L"devcon", nullptr, 0, argv.size() - 1, argv.data());
#else
    usb_manager_.DisableTargetDevices();
#endif
}

void USBProtector::ParsePolicy(const string &msg)
{
    try
    {
        json j = json::parse(msg);
        json jroot = json::parse(j[0].get<string>());
        if (jroot.at("type").get<string>() != "dev_control")
            return;

        QLogInfoUtf8(LOG_NAME, "policy: %s", msg.c_str());
        json jvalue = json::parse(jroot.at("value").get<string>());

        USBProtectorPolicy temp_policy = json::parse(jvalue.at("policy").get<string>());
        std::lock_guard<std::mutex> lg(curr_policy_mtx_);
        if (temp_policy == curr_policy_)
        {
            QLogInfoUtf8(LOG_NAME, "The current policy is the same as the last time, "
                                   "so the policy will not be updated.");
            return;
        }

        curr_policy_ = temp_policy;

        if (curr_policy_.isOpen)
        {
#if defined(_WIN32)
            auto devices = usb_device::EnumerateUsbDevices();
#else
            auto devices = usb_manager_.EnumTargetDevices();
#endif
            for (const auto &device : devices)
            {
                json alert_info_json = GenAlertInfo(device, curr_policy_);
                report_msg_queue_.Push(alert_info_json.dump(-1, ' '));
            }
        }
    }
    catch (const std::exception &e)
    {
        QLogErrorUtf8(LOG_NAME, "HandleAgentProxyMsg error: %s", e.what());
    }
}

void USBProtector::ParseLocalPolicy()
{
    QLogInfoUtf8(LOG_NAME, "============ Begin load local policy ============");
    auto policy_str = ReadFileToStr(kDevControlPolicyPath);
    if (!policy_str.empty())
    {
        json wrap_array = json::array({policy_str});
        string wrap_array_str = wrap_array.dump(-1, ' ');
        ParsePolicy(wrap_array_str);
    }
    QLogInfoUtf8(LOG_NAME, "============ Load local policy completed ============");
}

#if defined(_WIN32)
AlertInfo USBProtector::GenAlertInfo(const usb_device::DeviceInfo &dev_info, const USBProtectorPolicy &policy, bool is_monitoring)
{
    AlertInfo alert_info{};
    switch (dev_info.device_class)
    {
    case usb_device::DeviceClass::DiskDrive:
        alert_info.dev_type = STORAGE_DEV;
        alert_info.is_enabled = policy.storage_dev.is_enabled;
        break;
    case usb_device::DeviceClass::WPD:
        alert_info.dev_type = PORTABLE_DEV;
        alert_info.is_enabled = policy.portable_dev.is_enabled;
        break;
    case usb_device::DeviceClass::CDROM:
        alert_info.dev_type = CDROM_DEV;
        alert_info.is_enabled = policy.cdrom.is_enabled;
        break;
    case usb_device::DeviceClass::Net:
        alert_info.dev_type = WIFI_CARD;
        alert_info.is_enabled = policy.wifi_card.is_enabled;
        break;
    default:
        break;
    }
    if (alert_info.is_enabled)
    {
        // windows 中设备已经是禁用状态，再启用，会触发 DBT_DEVICEARRIVAL 事件
        if (!dev_info.is_enabled && !is_monitoring)
        {
            kIgnoreDevEvent = true; // 忽略即将产生的事件
        }
        EnableUSBStorage(dev_info.instance_id);
        if (!dev_info.is_enabled && !is_monitoring)
        {
            Sleep(100);
            kIgnoreDevEvent = false;
        }
    }
    else
    {
        DisableUSBStorage(dev_info.instance_id);
    }

    alert_info.dev_path = to_byte_string(dev_info.instance_id);
    alert_info.vendor_id = dev_info.VendorIdToString();
    alert_info.product_id = dev_info.ProductIdToString();
    alert_info.product_name = to_byte_string(dev_info.product_name);
    alert_info.manufacturer = to_byte_string(dev_info.manufacturer);
    alert_info.serial = to_byte_string(dev_info.serial_number);
    alert_info.timestamp = time(nullptr);
    return alert_info;
}

#else
AlertInfo USBProtector::GenAlertInfo(const usb_manager::UsbDevice &dev_info, const USBProtectorPolicy &policy)
{
    AlertInfo alert_info{};
    switch (dev_info.device_type)
    {
    case usb_manager::DeviceType::kUsbStorage:
    case usb_manager::DeviceType::kStorageDevice:
        alert_info.dev_type = STORAGE_DEV;
        alert_info.is_enabled = policy.storage_dev.is_enabled;
        break;
    case usb_manager::DeviceType::kSmartphone:
        alert_info.dev_type = PORTABLE_DEV;
        alert_info.is_enabled = policy.portable_dev.is_enabled;
        break;
    case usb_manager::DeviceType::kCdromDevice:
        alert_info.dev_type = CDROM_DEV;
        alert_info.is_enabled = policy.cdrom.is_enabled;
        break;
    case usb_manager::DeviceType::kWifiAdapter:
        alert_info.dev_type = WIFI_CARD;
        alert_info.is_enabled = policy.wifi_card.is_enabled;
        break;
    default:
        break;
    }
    if (alert_info.is_enabled)
    {
        EnableUSBStorage(dev_info);
    }
    else
    {
        DisableUSBStorage(dev_info);
    }

    alert_info.dev_path = dev_info.syspath;
    alert_info.vendor_id = dev_info.VendorIdToString();
    alert_info.product_id = dev_info.ProductIdToString();
    alert_info.product_name = dev_info.product_name;
    alert_info.manufacturer = dev_info.manufacturer;
    alert_info.serial = dev_info.serial;
    alert_info.timestamp = time(nullptr);
    return alert_info;
}
#endif

template <typename T>
void USBProtector::EnableUSBStorage(const T &device)
{
#if defined(_WIN32)
    wstring cmd = L"@" + device;
    std::vector<wchar_t> cmd_buf(cmd.begin(), cmd.end());
    cmd_buf.push_back('\0');
    PTSTR argv[] = {cmd_buf.data(), nullptr};
    cmdEnable(L"devcon", nullptr, 0, 1, argv);
#else
    usb_manager_.EnableDevice(device);
#endif
}

template <typename T>
void USBProtector::DisableUSBStorage(const T &device)
{
#if defined(_WIN32)
    wstring cmd = L"@" + device;
    std::vector<wchar_t> cmd_buf(cmd.begin(), cmd.end());
    cmd_buf.push_back('\0');
    PTSTR argv[] = {cmd_buf.data(), nullptr};
    int ret = cmdDisable(L"devcon", nullptr, 0, 1, argv);
    QLogInfoUtf8(LOG_NAME, "DisableUSBStorage ret: %d", ret);
#else
    usb_manager_.DisableDevice(device);
#endif
}
