#include "alert_info.h"

void to_json(json& j, const AlertInfo& a) {
    j = json{
        {"dev_path", a.dev_path},
        {"vendor_id", a.vendor_id},
        {"product_id", a.product_id},
        {"product_name", a.product_name},
        {"manufacturer", a.manufacturer},
        {"serial", a.serial},
        {"rule_id", a.rule_id},
        {"type", a.type},
        {"dev_type", a.dev_type},
        {"timestamp", a.timestamp},
        {"is_enabled", a.is_enabled}
    };
}

void from_json(const json& j, AlertInfo& a) {
    j.at("dev_path").get_to(a.dev_path);
    j.at("vendor_id").get_to(a.vendor_id);
    j.at("product_id").get_to(a.product_id);
    j.at("product_name").get_to(a.product_name);
    j.at("manufacturer").get_to(a.manufacturer);
    j.at("serial").get_to(a.serial);
    j.at("rule_id").get_to(a.rule_id);
    j.at("type").get_to(a.type);
    j.at("dev_type").get_to(a.dev_type);
    j.at("timestamp").get_to(a.timestamp);
    j.at("is_enabled").get_to(a.is_enabled);
}