#include "sign_verify.h"

SignVerify::SignVerify()
{
	fsv_.Init();
}

SignVerify::~SignVerify()
{
	fsv_.UnInit();
}

bool SignVerify::VerifySign(const std::wstring& filepath)
{
	CStringW strSigner;
	SYSTEMTIME stTimeStamp = { 0 };
	BOOL bCacheable = FALSE;
	HRESULT hrSelf = fsv_.OnlyVerifyFileHash(filepath.c_str(), strSigner, &bCacheable, &stTimeStamp);
	if (SUCCEEDED(hrSelf))
	{
		return TRUE;
	}
	HRESULT hrCat = E_NOTIMPL;
	HCATADMIN hCatAdmin = NULL;
	PBYTE pbFileHash = NULL;
	DWORD dwFileHashLength = 0;
	CStringW strCatalogName;
	bCacheable = FALSE;
	hrCat = fsv_.GetCatalogFromFile(filepath.c_str(), &pbFileHash, &dwFileHashLength, &hCatAdmin, strCatalogName, &bCacheable);
	if (SUCCEEDED(hrCat))
	{
		hrCat = fsv_.GetSignatureFromCatalog(pbFileHash, dwFileHashLength, filepath.c_str(), (LPCWSTR)strCatalogName, hCatAdmin, strSigner);
	}
	return SUCCEEDED(hrCat);
}
