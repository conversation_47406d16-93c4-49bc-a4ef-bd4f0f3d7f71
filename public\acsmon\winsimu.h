#pragma once

#include <gendef.h>

EXTERN_C_START

#ifdef __LINUX__

#ifndef GUID_DEFINED
#define GUID_DEFINED

typedef struct __GUID {
	unsigned int   Data1;
	unsigned short Data2;
	unsigned short Data3;
	unsigned char  Data4[8];
} GUID;

typedef GUID UUID;
#endif

struct _UNICODE_STRING {
	USHORT Length;
	USHORT MaximumLength;
	kchar* Buffer;
};
typedef struct _UNICODE_STRING UNICODE_STRING;
typedef struct _UNICODE_STRING* PUNICODE_STRING;
#endif


#define MAX_USTRING 0xffff

VOID
RtlInitUnicodeString(
	PUNICODE_STRING DestinationString,
	kchar* SourceString
);

VOID
RtlFreeUnicodeString(
	PUNICODE_STRING UnicodeString
);

NTSTATUS
RtlStringFromGUID(
	GUID* Guid,
	PUNICODE_STRING GuidString
);

NTSTATUS
RtlGUIDFromString(
	PUNICODE_STRING GuidString,
	GUID* Guid
);

bool_t
RtlEqualUnicodeString(
	PUNICODE_STRING String1,
	PUNICODE_STRING String2,
	bool_t CaseInSensitive
);

EXTERN_C_END