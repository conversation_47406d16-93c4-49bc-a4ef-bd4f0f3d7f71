#pragma once

#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>

namespace usb_device {

    enum class DeviceClass {
        Unknown,
        DiskDrive,
        CDROM,
        Net,
        WPD
    };

    struct DeviceInfo {
        std::wstring product_name;
        std::wstring manufacturer;
        std::wstring serial_number;
        std::wstring hardware_id;
        std::wstring instance_id;
        DeviceClass device_class = DeviceClass::Unknown;
        uint16_t vendor_id = 0;
        uint16_t product_id = 0;
        bool is_enabled = true;  // 设备是否启用
        
        // 将 vendor_id 转换为 0x%04X 格式的字符串
        std::string VendorIdToString() const;
        
        // 将 product_id 转换为 0x%04X 格式的字符串
        std::string ProductIdToString() const;
    };

    // 从硬件ID解析VID和PID
    bool ParseVidPid(const std::wstring& hardware_id, uint16_t& vid, uint16_t& pid);

    // 从实例ID提取序列号
    std::wstring ExtractSerialNumber(const std::wstring& instance_id);

    // 获取设备属性
    std::wstring GetDeviceProperty(HDEVINFO dev_info, SP_DEVINFO_DATA* dev_data, DWORD property);

    // 通过设备实例句柄获取设备属性
    std::wstring GetDevicePropertyByDevinst(DEVINST dev_inst, ULONG property);

    // 获取设备实例ID
    std::wstring GetDeviceInstanceId(DEVINST dev_inst);

    // 检查设备是否为USB设备
    bool IsUsbDevice(const std::wstring& instance_id);

    // 检查设备是否启用
    bool IsDeviceEnabled(HDEVINFO dev_info, PSP_DEVINFO_DATA dev_data);

    // 从父设备链中查找USB设备信息
    void FillUsbInfoFromParents(DEVINST dev_inst, DeviceInfo& info);

    // 检查是否为目标设备类型
    bool IsTargetDeviceClass(DeviceClass device_class);

    // 将设备类字符串转换为枚举
    DeviceClass StringToDeviceClass(const std::wstring& device_class_str);

    // 将 vendor_id 转换为 0x%04X 格式的字符串
    std::wstring VendorIdToString(uint16_t vendor_id);

    // 将 product_id 转换为 0x%04X 格式的字符串
    std::wstring ProductIdToString(uint16_t product_id);

    // 枚举USB设备
    std::vector<DeviceInfo> EnumerateUsbDevices();

    // 通过设备实例ID获取设备信息
    DeviceInfo GetDeviceInfoByInstanceId(const std::wstring& instance_id);
    
    // 打印设备信息
    void PrintDeviceInfo(const DeviceInfo& device);

} // namespace usb_device