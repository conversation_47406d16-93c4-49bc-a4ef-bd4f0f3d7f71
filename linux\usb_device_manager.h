// usb_device_manager.h
#ifndef USB_DEVICE_MANAGER_H_
#define USB_DEVICE_MANAGER_H_

#include <iostream>
#include <string>
#include <vector>
#include <set>
#include <algorithm>
#include <libudev.h>

namespace usb_manager {

enum class DeviceType {
  kUnknown = 0,
  kUsbStorage,      // U盘存储设备
  kCdromDevice,     // CD-ROM设备
  kWifiAdapter,     // WiFi网卡
  kSmartphone,      // 智能手机数据传输
  kStorageDevice,   // 存储设备
  kUsbHub,          // USB Hub
  kCompositeDevice, // 复合设备
  kOtherDevice      // 其他设备
};

// USB设备信息结构体
struct UsbDevice {
  std::string action;
  std::string real_syspath;
  std::string syspath;
  std::string devpath;
  // std::string devnode;      // 设备节点路径
  std::string vendor_id;
  std::string product_id;
  std::string device_class;
  std::string device_subclass;
  std::string device_protocol;
  std::string interface_class;
  std::string interface_subclass;
  std::string product_name;
  std::string manufacturer;
  std::string serial;
  // std::string id_type;      // ID_TYPE属性
  bool authorized;
  DeviceType device_type = DeviceType::kUnknown;
  std::string bus_num;
  std::string dev_num;

  // 转换函数
  std::string VendorIdToString() const;     // 转换vendor_id为0x1111格式字符串
  std::string ProductIdToString() const;    // 转换product_id为0x1111格式字符串
  unsigned int VendorIdToUint() const;      // 转换vendor_id为unsigned int
  unsigned int ProductIdToUint() const;     // 转换product_id为unsigned int
  std::string ToString() const;             // 转换整个结构体为字符串

  // 比较操作符
  // bool operator==(const UsbDevice& other) const;
  // bool operator!=(const UsbDevice& other) const;
};

// USB设备管理器类
class UsbDeviceManager {
 public:
  UsbDeviceManager();
  ~UsbDeviceManager();

  // 禁用拷贝构造和赋值操作
  UsbDeviceManager(const UsbDeviceManager&) = delete;
  UsbDeviceManager& operator=(const UsbDeviceManager&) = delete;

  // 扫描所有USB设备
  std::vector<UsbDevice> ScanUsbDevices();

  // 禁用/启用设备
  bool DisableDevice(const std::string& syspath);
  bool EnableDevice(const std::string& syspath);
  bool DisableDevice(const UsbDevice& device);
  bool EnableDevice(const UsbDevice& device);

  // 批量操作目标设备
  int DisableTargetDevices();
  int EnableTargetDevices();

  // 检查是否为目标设备类型
  bool IsTargetDevice(const UsbDevice& device);

  // 解析单个USB设备信息通过 usb_interface 
  UsbDevice ParseUsbDeviceByInterface(struct udev_device* udev_device);

  // 打印设备信息
  void PrintDeviceInfo(const UsbDevice& device);

 private:
  struct udev* udev_ctx_;

  // WiFi设备厂商ID集合
  std::set<std::string> wifi_vendor_ids_;
  
  // 智能手机厂商ID集合
  std::set<std::string> phone_vendor_ids_;

  // 初始化厂商ID列表
  void InitializeVendorIds();

  // 解析单个USB设备信息
  UsbDevice ParseUsbDevice(struct udev_device* udev_device);

  // 获取设备属性
  std::string GetDeviceAttribute(struct udev_device* udev_device, 
                                const char* attr_name);
  
  // 获取设备属性值（通过property）
  std::string GetDeviceProperty(struct udev_device* udev_device,
                               const char* property_name);

  // 获取接口信息
  void GetInterfaceInfo(UsbDevice& device);

  // 设备类型识别
  DeviceType IdentifyDeviceType(const UsbDevice& device);
  bool IsWifiDevice(const UsbDevice& device);
  bool IsSmartphoneDevice(const UsbDevice& device);
  DeviceType CheckCompositeDeviceType(const UsbDevice& device);

  // 字符串处理辅助函数
  std::string ToLowerCase(const std::string& str);
  std::string TruncateString(const std::string& str, size_t max_length);
};

}  // namespace usb_manager

#endif  // USB_DEVICE_MANAGER_H_

