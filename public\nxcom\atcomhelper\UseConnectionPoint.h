
#ifndef _ATUSECONNECTIONPOINT_HELPER_H_
#define _ATUSECONNECTIONPOINT_HELPER_H_

#include <atcom/connectionpoint.h>
#include <vector>

namespace atsdk {

template<class iid>
class UseConnectPoint
{
public:
	UTIL::com_ptr<IAtConnectionPoint> m_pConnectPoint;
	DWORD	m_dwConnectPointCookie;
	BOOL	m_bConnected;

	UseConnectPoint():m_dwConnectPointCookie(-1),m_bConnected(FALSE){};
	~UseConnectPoint(){DisConnect();}

	HRESULT Connect(IUnknown* pConnectionPointContainer, IUnknown* pThis)
	{
		UTIL::com_ptr<IAtConnectionPointContainer> pCPC(pConnectionPointContainer);
		RASSERT(pCPC, E_FAIL);

		RFAILED(pCPC->FindConnectionPoint(__uuidof(iid), (IAtConnectionPoint**)&m_pConnectPoint));
		return Connect(pThis);
	}

	HRESULT Connect(IUnknown* pThis)
	{
		RASSERT(m_pConnectPoint, E_FAIL);
		
		UTIL::com_ptr<iid> pMCP(pThis);
		RFAILED(m_pConnectPoint->Advise(pMCP, &m_dwConnectPointCookie));

		m_bConnected = TRUE;
		return S_OK;
	}

	HRESULT DisConnect()
	{
		if(m_pConnectPoint)
		{
			if(m_bConnected)
				m_pConnectPoint->Unadvise(m_dwConnectPointCookie);
			m_pConnectPoint = INULL;
		}

		m_bConnected = FALSE;
		return FALSE;
	}
};

} //namespace atsdk

#endif //_ATUSECONNECTIONPOINT_HELPER_H_