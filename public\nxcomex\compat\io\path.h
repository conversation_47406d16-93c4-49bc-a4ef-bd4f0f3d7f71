#ifndef _COMPAT_PATH_H_
#define _COMPAT_PATH_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C"
{
#endif

int GetDynamicPath(void* szName, basic_tchar* szPath, ULONG ulen);
int FileIsExist(basic_tchar* lpFile);
int GetExecutePath(HINSTANCE hInstance, basic_tchar* szPath, ULONG ulen);
int GetDynamicName(const basic_tchar* pName, basic_tchar* pPluginName, ULONG ulen);

#ifdef	__cplusplus
}
#endif

#endif


