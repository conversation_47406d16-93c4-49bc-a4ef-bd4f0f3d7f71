qlogger使用源码方式提供：
* 废除之前的静态库方式，因为linux上因为系统版本的不同会造成各种编译问题。

* 现在使用qlogger源码方式，需要编译机器上有log4cplus的相关头文件以及lib库才行。
* 目前log4cplus的代码在ssh://********************:10022/acs/third/log4cplus-2.x.git位置存放
* 开发人员自己配置自己的log4cplus，这里就不复杂了
* 打包机编译配置：
  linux: /home/<USER>/third/build/ 
  windows: C:\Packages\third\build
* yml文件配置：
  linux:   ln -s $HOME/third ./third
  windows:
	- if(test-path third){ if((get-item third).Mode.Contains("l")){(Get-Item third).Delete()}else{remove-item third -recurse}}
    	- New-Item -Path third -ItemType SymbolicLink -Value c:\packages\third
  备注：上述代码是将系统的内已经编译好的third库 软连接到当前位置。这里可以根据自己需要调整，所以的代码都在third\build下：


  备注：windows如果不想使用源码：可以只使用qlogger.h头文件，然后使用public/lib下已经编译好的静态库。但是linux必须使用源码方式