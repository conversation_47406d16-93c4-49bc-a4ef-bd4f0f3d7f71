#include "process_manager.h"
#include <thread>
#include <boost/process.hpp>
#include <chrono>
#include <set>

#if defined(_WIN32)
#include <windows.h>
#include <tlhelp32.h>
#include <Psapi.h>
#else
#include <sys/types.h>
#include <dirent.h>
#endif


ProcManager::ProcManager()
{
	proc_watchdog_thread_ = std::make_shared<std::thread>(&ProcManager::ProcWatchDog, this);
	watchdog_isopen = true;
}

ProcManager::~ProcManager()
{
	watchdog_isopen = false;
	proc_watchdog_thread_->join();
}

bool ProcManager::StartupProcByPath(const tstring& proc_path)
{
	if (!IsProcRunning(proc_path))
	{
		try
		{
			std::lock_guard<std::mutex> lg(cache_mtx_);

			run_proc_cache_.erase(proc_path);

#if defined(_WIN32)
			std::wstring build_proc_path = proc_path;
			build_proc_path.insert(0, L"\"");
			build_proc_path.append(L"\"");
			auto exec_handle = std::make_shared<bp::child>(build_proc_path);
#else
			auto exec_handle = std::make_shared<bp::child>(proc_path);
#endif
			std::thread t([=]() {
				try
				{
					exec_handle->wait();
				}
				catch (...)
				{
					return;
				}
			});
			t.detach();
			run_proc_cache_.insert(std::make_pair(proc_path, exec_handle));

			auto it = proc_status_.find(proc_path);
			if (it != proc_status_.end())
			{
				it->second = ProcStatus::kStartup;
			}
			else
			{
				proc_status_.insert(std::make_pair(proc_path, ProcStatus::kStartup));
			}

		}
		catch (...)
		{
			return false;
		}
	}

	return true;
}

bool ProcManager::StopProcByPath(const tstring& proc_path)
{
	std::lock_guard<std::mutex> lg(cache_mtx_);

	auto proc_status_it = proc_status_.find(proc_path);
	if (proc_status_it != proc_status_.end())
	{
		proc_status_it->second = ProcStatus::kStop;
	}
	else
	{
		proc_status_.insert(std::make_pair(proc_path, ProcStatus::kStop));
	}

	auto it = run_proc_cache_.find(proc_path);
	if (it != run_proc_cache_.end())
	{
		try
		{
			it->second->terminate();
		}
		catch (...)
		{

		}
	}

	// Check again whether the process exists.
	std::vector<bp::pid_t> pids{};
	if (IsProcRunning(proc_path, pids))
	{
		for (auto proc_pid : pids)
		{
			try
			{
				bp::child::child_handle pid{ proc_pid };
				bp::detail::api::terminate(pid);
			}
			catch (...)
			{

			}
		}

	}

	return true;
}

bool ProcManager::IsProcRunning(const tstring& proc_path)
{
	std::vector<bp::pid_t> pids{};
	return IsProcRunning(proc_path, ProcInfoEnum::kOnlyProcRun, pids);
}

bool ProcManager::IsProcRunning(const tstring& proc_path, std::vector<bp::pid_t>& pids)
{
	return IsProcRunning(proc_path, ProcInfoEnum::kGetProcRunPid, pids);
}

bool ProcManager::IsProcRunning(const tstring& proc_path, ProcInfoEnum flags, std::vector<bp::pid_t>& pids)
{
#if defined(_WIN32)
	HANDLE hProcessSnap;
	PROCESSENTRY32 pe32;
	pe32.dwSize = sizeof(PROCESSENTRY32);
	hProcessSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);

	if (hProcessSnap == INVALID_HANDLE_VALUE) {
		return false;
	}

	bool is_running = false;

	if (Process32First(hProcessSnap, &pe32)) {
		do {
			if (0 == pe32.th32ProcessID)
				continue;

			TCHAR szProcessPath[MAX_PATH];
			HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pe32.th32ProcessID);
			if (hProcess) {
				if (GetModuleFileNameEx(hProcess, NULL, szProcessPath, MAX_PATH) != 0) {
					if (0 == _wcsicmp(szProcessPath, proc_path.c_str()))
					{
						is_running = true;
						pids.push_back(pe32.th32ProcessID);
					}
				}
				CloseHandle(hProcess);
			}

			if (is_running && ProcInfoEnum::kOnlyProcRun == flags)
			{
				break;
			}

		} while (Process32Next(hProcessSnap, &pe32));
	}

	CloseHandle(hProcessSnap);

	return is_running;
#else
	DIR* dp;
	struct dirent* entry;
	bool is_running = false;

	dp = opendir("/proc");
	if (dp)
	{
		while ((entry = readdir(dp)) != NULL)
		{
			if ((strcmp(entry->d_name, ".") == 0) ||
				(strcmp(entry->d_name, "..") == 0) ||
				(atoi(entry->d_name) <= 0))
			{
				continue;
			}

			pid_t pid = atoi(entry->d_name);

			char temp_path[256] = { 0 };
			char real_exe_path[4096] = { 0 };
			ssize_t link_len = 0;
			sprintf(temp_path, "/proc/%d/exe", pid);

			link_len = readlink(temp_path, real_exe_path, sizeof(real_exe_path));
			if (-1 == link_len)
			{
				continue;
			}

			if (0 == strcmp(real_exe_path, proc_path.c_str()))
			{
				is_running = true;
				pids.push_back(pid);
			}

			if (is_running && ProcInfoEnum::kOnlyProcRun == flags)
			{
				break;
			}
		}

		closedir(dp);
	}

	return is_running;
#endif
}

void ProcManager::ProcWatchDog()
{
	while (watchdog_isopen)
	{
		std::set<tstring> need_statup_proc;
		{
			std::lock_guard<std::mutex> lg(cache_mtx_);
			for (const auto& proc_info : proc_status_)
			{
				if (ProcStatus::kStartup == proc_info.second)
				{
					if (!IsProcRunning(proc_info.first))
					{
						need_statup_proc.insert(proc_info.first);
					}
				}
			}
		}

		for (auto& path : need_statup_proc)
		{
			StartupProcByPath(path);
		}

		std::this_thread::sleep_for(std::chrono::seconds(10));
	}
}
