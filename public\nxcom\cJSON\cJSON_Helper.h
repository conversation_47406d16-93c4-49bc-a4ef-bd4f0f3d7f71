#pragma once
#include "cJSON.h"
#include <string>
#include <windows.h>
#include <memory>

cJSON* cJSON_ParseFile(LPCTSTR lpszFile);

unsigned int cJSON_GetItemNumber(cJSON* object, const char* itemName, unsigned int def = 0);
std::string cJSON_GetItemString(cJSON* object, const char* itemName, char* def = nullptr);
std::wstring cJSON_GetItemWString(cJSON* object, const char* itemName, char* def = nullptr);

////////////////////////////////////////

class AutoFreeCJSONImpl;

class AutoFreeCJSON
{
public:
	AutoFreeCJSON();

	~AutoFreeCJSON();

	void operator=(cJSON* pJson);

	void operator=(AutoFreeCJSON& r);

	cJSON* GetPtr();

	void ClearPtr();

private:

	std::shared_ptr<AutoFreeCJSONImpl> m_spRoot;
};