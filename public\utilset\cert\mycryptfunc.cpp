
#include "mycryptfunc.h"
#include <Softpub.h>
#include <mscat.h>
#include "mssip32/mssip32.h"

#pragma comment(lib, "Imagehlp.lib")

typedef HANDLE HCATADMIN;
typedef HANDLE HCATINFO;

// {DE351A42-8E59-11D0-8C47-00C04FC295EE}
const GUID CRYPT_SUBJTYPE_FLAT_IMAGE = { \
0xDE351A42, 0x8E59, 0x11D0, 0x8C, 0x47, 0x00, 0xC0, 0x4F, 0xC2, 0x95, 0xEE};

// {c689aab8-8e78-11d0-8c47-00c04fc295ee}
const GUID PE_SUBJECT_TYPE = { \
0xc689aab8, 0x8e78, 0x11d0, 0x8c, 0x47, 0x00, 0xc0, 0x4f, 0xc2, 0x95, 0xee};

#define     MAX_HASH_LEN                20  // SHA1
#ifdef __cplusplus
#       define          DELETE_OBJECT(obj0)     if (obj0)           \
{                   \
	delete obj0;    \
	obj0 = NULL;    \
}
#else
#       define          DELETE_OBJECT(obj0)     if (obj0)           \
{                   \
	free(obj0);     \
	obj0 = NULL;    \
}
#endif

#define TRACE_ERROR_EX(ss, label) \
label: \
	goto ErrorReturn

#define SET_ERROR_VAR_EX(ss, label, err) \
label: \
	::SetLastError(err); \
	goto ErrorReturn

BOOL WINAPI MyCryptCATAdminCalcHashFromFileHandle(HANDLE hFile, DWORD *pcbHash, BYTE *pbHash, DWORD dwFlags)
{
	GUID                gFlat = CRYPT_SUBJTYPE_FLAT_IMAGE;
	BYTE                *pbRet;
	SIP_INDIRECT_DATA   *pbIndirectData;
	BOOL                fRet;

	pbIndirectData  = NULL;
	pbRet           = NULL;

	if (!(hFile) ||
		(hFile == INVALID_HANDLE_VALUE) ||
		!(pcbHash) ||
		(dwFlags != 0))
	{
		goto InvalidParam;
	}

	GUID                gSubject;
	SIP_DISPATCH_INFO   sSip;

	if (!(CryptSIPRetrieveSubjectGuid(L"CATADMIN", hFile, &gSubject)))
	{
		memcpy(&gSubject, &gFlat, sizeof(GUID));
	}

	memset(&sSip, 0x00, sizeof(SIP_DISPATCH_INFO));

	sSip.cbSize = sizeof(SIP_DISPATCH_INFO);

	if (!(CryptSIPLoad(&gSubject, 0, &sSip)))
	{
		goto SIPLoadError;
	}

	SIP_SUBJECTINFO     sSubjInfo;
	DWORD               cbIndirectData;

	memset(&sSubjInfo, 0x00, sizeof(SIP_SUBJECTINFO));
	sSubjInfo.cbSize                    = sizeof(SIP_SUBJECTINFO);

	sSubjInfo.DigestAlgorithm.pszObjId  = (char *)CertAlgIdToOID(CALG_SHA1);

	sSubjInfo.dwFlags                   = SPC_INC_PE_RESOURCES_FLAG | SPC_INC_PE_IMPORT_ADDR_TABLE_FLAG |
		MSSIP_FLAGS_PROHIBIT_RESIZE_ON_CREATE;
	sSubjInfo.pgSubjectType             = &gSubject;
	sSubjInfo.hFile                     = hFile;
	sSubjInfo.pwsFileName               = L"CATADMIN";

	sSubjInfo.dwEncodingType            = PKCS_7_ASN_ENCODING | X509_ASN_ENCODING;

	cbIndirectData = 0;

	mssip_CryptSIPCreateIndirectData(&sSubjInfo, &cbIndirectData, NULL);

	if (cbIndirectData == 0)
	{
		SetLastError( E_NOTIMPL );
		goto SIPError;
	}

	if (!(pbIndirectData = (SIP_INDIRECT_DATA   *)new BYTE[cbIndirectData]))
	{
		goto MemoryError;
	}

	if (!(mssip_CryptSIPCreateIndirectData(&sSubjInfo, &cbIndirectData, pbIndirectData)))
	{
		if ( GetLastError() == 0 )
		{
			SetLastError( ERROR_INVALID_DATA );
		}
		goto SIPError;
	}
	if ((pbIndirectData->Digest.cbData == 0) || (pbIndirectData->Digest.cbData > MAX_HASH_LEN))
	{
		SetLastError( ERROR_INVALID_DATA );
		goto SIPError;
	}

	if (!(pbRet = new BYTE[pbIndirectData->Digest.cbData]))
	{
		goto MemoryError;
	}

	memcpy(pbRet, pbIndirectData->Digest.pbData, pbIndirectData->Digest.cbData);

	fRet = TRUE;

CommonReturn:
	if (pbRet)
	{
		if (*pcbHash < pbIndirectData->Digest.cbData)
		{
			SetLastError(ERROR_INSUFFICIENT_BUFFER);
			fRet = FALSE;
		}
		else if (pbHash)
		{
			memcpy(pbHash, pbRet, pbIndirectData->Digest.cbData);
		}

		*pcbHash = pbIndirectData->Digest.cbData;

		delete pbRet;
	}

	if (pbIndirectData)
	{
		delete pbIndirectData;
	}

	if ((GetLastError() == ERROR_INSUFFICIENT_BUFFER) && !(pbHash))
	{
		fRet = TRUE;
	}

	return(fRet);

ErrorReturn:
	DELETE_OBJECT(pbRet);
	fRet = FALSE;
	goto CommonReturn;

	TRACE_ERROR_EX(DBG_SS, SIPLoadError);
	TRACE_ERROR_EX(DBG_SS, SIPError);

	SET_ERROR_VAR_EX(DBG_SS, InvalidParam,      ERROR_INVALID_PARAMETER);
	SET_ERROR_VAR_EX(DBG_SS, MemoryError,       ERROR_NOT_ENOUGH_MEMORY);
}