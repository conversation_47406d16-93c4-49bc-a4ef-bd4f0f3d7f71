#ifndef _UTIL_H_
#define _UTIL_H_


/*
					Windows					Linux
unsigned int		32 bits / 4294967295	32 bits
unsigned long		32 bits					64 bits / 18446744073709551615
unsigned long long	64 bits					64 bits
size_t				32 bits					64 bits

					32bits		64bits			format
int					4			4				%d
short				2			2				%d
long 				4			8				%ld
long long			8			8				%lld
unsigned 			4			4				%u
unsigned short		2			2				%u
unsigned long		4			8				%lu
unsigned long long	8			8				%llu
*/



//Data Type 	ILP32 					LP32 		ILP64 	LP64 			LLP64
//#define 		_ 						_ 			_ 		__LP64__ 		__LLP64__
//target 		(Win32API,Linux,MacOSX) Win16 API 	_		Linux,MacOSX 	Win64 API
//char 			8 						8 			8 		8 				8
//short 		16 						16 			16 		16 				16
//int 			32 						16 			64 		32 				32
//long 			32 						32 			64 		64 				32
//long long		64 						64 			64 		64 				64
//pointer 		32 						32 			64 		64 				64

/*
In this table, LP64, ILP64, LLP64 are the word length models for 64-bit platforms, 
and ILP32 and LP32 are the word length models for 32-bit platforms.

LP64 means long and pointer are 64 bits,
ILP64 means int, long, pointer is 64 bits,
LLP64 means long long and pointer are 64-bit.
ILP32 means int, long, and pointer are 32 bits,
LP32 means long and pointer are 32 bits.
Float is all 4 bytes;
Double is 8 bytes; C = double; C = double; C = double


The difference between the three 64-bit models (LP64, LLP64, and ILP64) is the non-floating point data type. 
When the width of one or more C data types changes from one model to another, 
applications can be affected in many ways. These effects can be divided into two main categories:

The size of the data object. 
The compiler aligns data types along natural boundaries; 
In other words, 32-bit data types are aligned along 32-bit boundaries on 64-bit systems, and 64-bit data types are aligned along 64-bit boundaries on 64-bit systems. This means that the size of data objects, such as structures or unions, is different on 32-bit and 64-bit systems.

The size of the base data type. The usual assumptions about relationships between basic data types are broken on the 64-bit data model. 
Applications that rely on these relationships will also fail to compile on 64-bit platforms. For example, the assumption that sizeof (int) = sizeof (long) = sizeof (pointer) is valid for the ILP32 data model, but not for any other data model.


In summary, the compiler aligns data types along natural boundaries, which means that the compiler "padded" to enforce alignment in this way, just as it does in C structures and unions. The members of a structure or union are aligned according to the widest member.
*/

#include <util/core.hpp>
#include <util/tpid.hpp>
#include <util/sock.hpp>
#include <util/file.hpp>
#include <util/break.hpp>
#include <util/sleep.hpp>
#include <util/debug.hpp>
#include <util/guid.hpp>
#include <util/comutil.hpp>
#include <util/variant.hpp>
#include <util/assert.hpp>

#endif // !_UTIL_H_
