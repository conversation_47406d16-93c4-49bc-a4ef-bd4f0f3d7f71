#include <dlcom/cominc.h>

uvStdComNameSpaceBegin

	//////////////////////////////////////////////////////////////////////////
	//iplugin.h

	// {33B172BE-68E7-4640-8624-11749B1E0B1A}
	_DEFINE_IID_IMPL(IID_IPlugin,
		0x33b172be, 0x68e7, 0x4640, 0x86, 0x24, 0x11, 0x74, 0x9b, 0x1e, 0xb, 0x1a);

	// {B71AE479-F976-417A-9F12-46D9FAAD1E50}
	_DEFINE_IID_IMPL(IID_IPluginRun,
		0xb71ae479, 0xf976, 0x417a, 0x9f, 0x12, 0x46, 0xd9, 0xfa, 0xad, 0x1e, 0x50);

	//////////////////////////////////////////////////////////////////////////
	//unknown.h

	// {00000000-0000-0000-0000-000000000046}
	_DEFINE_IID_IMPL(IID_IBase,
		0x00000000, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46);

	// {00000001-0000-0000-0000-000000000046}
	_DEFINE_IID_IMPL(IID_IComClassFactory,
		0x00000001, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x46);

	// {00000000-0000-0000-0000-000000000000}
	_DEFINE_GUID_IMPL(COMPONENT_NULL,
		0x00000000, 0x0000, 0x0000, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00);

	//////////////////////////////////////////////////////////////////////////
	//icombase.h

	// {E670ECA1-E73C-4EE7-92D6-42C597254A38}
	_DEFINE_IID_IMPL(IID_ICompoentLoader,
		0xe670eca1, 0xe73c, 0x4ee7, 0x92, 0xd6, 0x42, 0xc5, 0x97, 0x25, 0x4a, 0x38);

	// {619CDF0D-DEA4-4A4E-8774-D2D51BE0B208}
	_DEFINE_IID_IMPL(IID_ILibManager,
		0x619cdf0d, 0xdea4, 0x4a4e, 0x87, 0x74, 0xd2, 0xd5, 0x1b, 0xe0, 0xb2, 0x8);

	// {6D9D2CF3-276A-426e-9041-FB5428DE44B1}
	_DEFINE_GUID_IMPL(CLSID_CObjectLoader,
		0x6d9d2cf3, 0x276a, 0x426e, 0x90, 0x41, 0xfb, 0x54, 0x28, 0xde, 0x44, 0xb1);

	// {08F56552-D015-4C3B-B984-2211A4F880FF}
	_DEFINE_IID_IMPL(IID_IComRunningObjectTable,
		0x8f56552, 0xd015, 0x4c3b, 0xb9, 0x84, 0x22, 0x11, 0xa4, 0xf8, 0x80, 0xff);

	// {4EBB368E-462C-4FF9-BEA4-71D843E8AB1B}
	_DEFINE_IID_IMPL(IID_IComRotMessage,
		0x4ebb368e, 0x462c, 0x4ff9, 0xbe, 0xa4, 0x71, 0xd8, 0x43, 0xe8, 0xab, 0x1b);

	// {513F39CB-04C7-4068-82DA-FAFE689D5EE4}
	_DEFINE_GUID_IMPL(ClSID_CComRunningObjectTable,
		0x513f39cb, 0x4c7, 0x4068, 0x82, 0xda, 0xfa, 0xfe, 0x68, 0x9d, 0x5e, 0xe4);

	// {70B0D10B-463F-496A-90A5-F22175F77A1D}
	_DEFINE_IID_IMPL(IID_IExit,
		0x70b0d10b, 0x463f, 0x496a, 0x90, 0xa5, 0xf2, 0x21, 0x75, 0xf7, 0x7a, 0x1d);

	// {321B84B2-ACE3-4EC4-9E0C-A63870839F07}
	_DEFINE_IID_IMPL(IID_IObjectRun,
		0x321b84b2, 0xace3, 0x4ec4, 0x9e, 0xc, 0xa6, 0x38, 0x70, 0x83, 0x9f, 0x7);

	// {7DADD097-97B4-45ec-A04C-135604FB6934}
	_DEFINE_GUID_IMPL(CLSID_CObjectRun,
		0x7dadd097, 0x97b4, 0x45ec, 0xa0, 0x4c, 0x13, 0x56, 0x4, 0xfb, 0x69, 0x34);

	//////////////////////////////////////////////////////////////////////////
	//ibase.h

	// {357C10F2-8A68-4138-BDE5-8C1C3896F7D5}
	_DEFINE_IID_IMPL(IID_IComObjectFrameworkClassFactory,
		0x357c10f2, 0x8a68, 0x4138, 0xbd, 0xe5, 0x8c, 0x1c, 0x38, 0x96, 0xf7, 0xd5);

	// {E2247B54-E329-4ca8-8361-6499FDFF98F4}
	_DEFINE_GUID_IMPL(CLSID_ComObjectFrameworkClassFactory,
		0xe2247b54, 0xe329, 0x4ca8, 0x83, 0x61, 0x64, 0x99, 0xfd, 0xff, 0x98, 0xf4);

	// {E9678781-A3CB-46fb-9121-3ED22C24CFAD}
	_DEFINE_GUID_IMPL(CLSID_STDCOM_ClassFactory,
		0xe9678781, 0xa3cb, 0x46fb, 0x91, 0x21, 0x3e, 0xd2, 0x2c, 0x24, 0xcf, 0xad);

	//////////////////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////////////////
	//imsg.h

	// {187EA2E8-A0F2-4DA9-9F1B-F492DCEEF8AD}
	_DEFINE_GUID_IMPL(IID_IMsg,
		0x187ea2e8, 0xa0f2, 0x4da9, 0x9f, 0x1b, 0xf4, 0x92, 0xdc, 0xee, 0xf8, 0xad);

	// {8b9e3502-d509-11eb-ac28-5f6601019609}
	_DEFINE_GUID_IMPL(IID_IMsgProxy,
		0x357c10f2, 0x8a68, 0x4138, 0xbd, 0xe5, 0x8c, 0x1c, 0x38, 0x96, 0xf7, 0xd5);

	// {5F8488ED-8DBA-4C96-9284-B712F846BB09}
	_DEFINE_GUID_IMPL(IID_IMsgPlugin,
		0x5f8488ed, 0x8dba, 0x4c96, 0x92, 0x84, 0xb7, 0x12, 0xf8, 0x46, 0xbb, 0x9);

	//////////////////////////////////////////////////////////////////////////

uvStdComNameSpaceEnd

#include "procsmgr_i/IAgentProxy.h"
_DEFINE_IID_IMPL(IID_IAgentProxy, 0x1CB19D51, 0x376D, 0x43C8, 0xBD, 0x4B, 0x1D, 0xB8, 0x5E, 0x36, 0xEC, 0x8B);

#include "procsmgr_i/IProcTree.h"
_DEFINE_IID_IMPL(IID_IProcTreeMgr,
	0xAB68D3E1, 0xED0F, 0x45F3, 0x86, 0xFA, 0xA5, 0x7D, 0x62, 0xFB, 0x35, 0x16);

_DEFINE_IID_IMPL(IID_IProcTreeInitWatcher,
	0x75b2960f, 0x85a6, 0x4373, 0xbc, 0x79, 0x18, 0xce, 0xa1, 0xa9, 0x20, 0x5e);

#include "procsmgr_i/IFileBackup.h"
_DEFINE_IID_IMPL(IID_IFileBackup,
	0x1ef4c4b2, 0x1ad1, 0x4c6f, 0x83, 0xf1, 0xc6, 0x72, 0x2b, 0xa4, 0x2f, 0x5c);


_DEFINE_IID_IMPL(IID_IProcHelper,
	0x86466876, 0xe4a, 0x410c, 0xb5, 0xe6, 0x96, 0x13, 0xd0, 0x27, 0x29, 0xcf);

#include "acsmon/dispcenter.h"

// {59E559AA-7306-4978-8B75-7879939D93A9}
_DEFINE_IID_IMPL(IID_IMsgDispCenter,
	0x59e559aa, 0x7306, 0x4978, 0x8b, 0x75, 0x78, 0x79, 0x93, 0x9d, 0x93, 0xa9);

#include "acsmon/msgpack.h"

// {513D8D23-DC26-4327-AAE8-348CCCFF8630}
_DEFINE_IID_IMPL(IID_IMsgPack,
	0x513d8d23, 0xdc26, 0x4327, 0xaa, 0xe8, 0x34, 0x8c, 0xcc, 0xff, 0x86, 0x30);
