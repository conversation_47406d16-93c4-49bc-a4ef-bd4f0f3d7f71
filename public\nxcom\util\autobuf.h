#ifndef autobuf_h__
#define autobuf_h__

#pragma once
namespace atsdk{
	namespace util{

		template<typename T>
		class autodelBuf{
		public:
			autodelBuf():p(NULL){}
			autodelBuf(T * pNew):p(pNew){}
			autodelBuf(size_t s):p(new T[s]){}
			// disallow
			autodelBuf(const autodelBuf & src);
			autodelBuf & operator = (const autodelBuf & src);
		public:
			autodelBuf & operator = (T*psrc){
				if(p){
					release();
				}
				p = psrc;
				return *this;
			}

		public:
			virtual ~autodelBuf(){
				release();		
			}
			operator T*(){
				return p;
			}
			operator void*(){
				return (void*)p;
			}
			void release(){
				std::wcout<<L"release"<<(LONG)this<<L" ptr:"<<(LONG)p<<std::endl;
				if(p)
					delete [] p;
				p = NULL;
			}
			T * detach(){
				T * pret = p;
				p = NULL;
				return pret;
			}
		private:
			T * p;

		};
	};
};
#endif // autobuf_h__