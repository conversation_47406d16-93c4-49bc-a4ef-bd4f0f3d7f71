stages:
  - build
before_script:
  - git submodule update --init --recursive
  - git submodule update
after_script:
  #- "curl -X POST --fail -F token=glptt-70b438c31c1135db0f4ff9f45801628499040820 -F ref=$CI_COMMIT_REF_NAME https://repo1.antiy.cn/api/v4/projects/1890/trigger/pipeline"
   
build-windows:
  stage: build
  only:
    - release-2.1
  script:
    - echo "Start  build hfwd for windows"
    - echo "git branch is $CI_COMMIT_REF_NAME"
    - if(test-path build){remove-item build -recurse}
    - mkdir build
    - cd build    
    - cmake .. -DAT_BUILD_PLATFORM=windows -DAT_OUT=c:\packages\$CI_COMMIT_REF_NAME
    - cmake --build . --config Release
  tags:
    - winall

build-linux:
  stage: build
  only:
    - release-2.1
  script:
    - echo "Start  build hfwd for linux-x86"
    - echo "git branch is $CI_COMMIT_REF_NAME"
    - rm -rf ./build
    - mkdir build
    - cd build    
    - cmake .. -DAT_BUILD_PLATFORM=linux
    - cmake --build .
  tags:
    - *************

build-linux-arm:
  stage: build
  only:
    - release-2.1
  script:
    - echo "Start  build hfwd for linux arm"
    - echo "git branch is $CI_COMMIT_REF_NAME"
    - rm -rf ./build
    - mkdir build
    - cd build
    - cmake .. -DAT_BUILD_PLATFORM=arm64
    - cmake --build .
  tags:
    - *************
