#ifndef _CONTAINER_RUN_HPP_
#define _CONTAINER_RUN_HPP_

#ifdef	__cplusplus

#include <dlcom/unknown.h>
#include <utilex/calldll.hpp>
#include <dlcom/loadcom.hpp>

class CContainer
{
public:
	CContainer() {
	
	}
	virtual ~CContainer() {

		m_pIObjectRun.dispose();
		m_loader.UnloadCom();
	}

	int CreateContainer(const basic_tchar* path,UINT len) {
		
		HRESULT hr = S_OK;

		m_pIObjectRun.dispose();
		m_loader.UnloadCom();

		basic_tchar szContainerName[DYNAMIC_NAME_LEN + 1] = { 0x00 };
		GetDynamicName(_T("container"), szContainerName, DYNAMIC_NAME_LEN);

		m_strContainer.clear();
		m_strContainer.append(path);
		m_strContainer.append(path_slash);
		m_strContainer.append(szContainerName);

		hr = m_loader.LoadCom(m_strContainer.c_str());
		rc_assert(hr == S_OK, S_ERROR)

		hr = m_loader.CreateInstance(CLSID_CObjectRun, INULL, INULL, IID_IObjectRun, (void**)&m_pIObjectRun);
		rc_assert(((hr == S_OK) && (m_pIObjectRun != INULL)), S_ERROR)

		hr = m_pIObjectRun->Clear();
		rc_assert(hr == S_OK, S_ERROR)

		hr = m_pIObjectRun->SetPath(path, len);
		rc_assert(hr == S_OK, S_ERROR)

		return S_SUCCESS;
	}
	int RegisterContainer(LPCSTR buf, ULONG len) {

		rc_assert(m_pIObjectRun != NULL, S_ERROR)
		rc_assert(m_pIObjectRun->RegisterCode(buf, len) == S_OK, S_ERROR)
		return S_SUCCESS;
	}

	int ExitContainer(HINSTANCE instance, UINT exit) {

		rc_assert(m_pIObjectRun != NULL, S_ERROR)
		rc_assert(m_pIObjectRun->NotifyExit(instance,exit) == S_OK, S_ERROR)
		return S_SUCCESS;
	}
	
	int InitContainer(HINSTANCE instance, basic_tchar* argv[], int argc) {

		rc_assert(m_pIObjectRun != NULL, S_ERROR)
		rc_assert(m_pIObjectRun->Init(instance, argv, argc) == S_OK, S_ERROR)
		return S_SUCCESS;
	}

	int StartContainer(HINSTANCE instance, UINT type) {

		rc_assert(m_pIObjectRun != NULL, S_ERROR)
		rc_assert(m_pIObjectRun->Start(instance, type) == S_OK, S_ERROR)
		return S_SUCCESS;
	}

	int StopContainer(HINSTANCE instance, UINT type, UINT exit) {

		rc_assert(m_pIObjectRun != NULL, S_ERROR)
		rc_assert(m_pIObjectRun->Stop(instance, type, exit) == S_OK, S_ERROR)
		return S_SUCCESS;
	}

	int RunContainer(HINSTANCE instance, basic_tchar* argv[], int argc, UINT type) {

		rc_assert(m_pIObjectRun != NULL, OBJECT_ERROR)
		UINT code = OBJECT_RUN_RET_SUCCESS;

		HRESULT hr = S_OK;
		hr = m_pIObjectRun->Init(instance, argv, argc);
		rc_assert(hr == S_OK, OBJECT_ERROR)


		logi("m_pIObjectRun->Start==>%u",type);
		hr = m_pIObjectRun->Start(instance, type);
		rc_assert(hr == S_OK, OBJECT_ERROR)

		code = m_pIObjectRun->GetExitCode();

		logi("m_pIObjectRun->Stop==>%u", code);
		hr = m_pIObjectRun->Stop(instance, type, code);
		rc_assert(hr == S_OK, OBJECT_ERROR)

		hr = m_pIObjectRun->Uninit(instance);
		rc_assert(hr == S_OK, OBJECT_ERROR)

		return S_SUCCESS;
	}

	int UnInitContainer(HINSTANCE instance) {

		rc_assert(m_pIObjectRun != NULL, S_ERROR)
		rc_assert(m_pIObjectRun->Uninit(instance) == S_OK, S_ERROR)
		return S_SUCCESS;
	}

	UINT GetContainerExitCode() {

		rc_assert(m_pIObjectRun != NULL, OBJECT_ERROR)
		return m_pIObjectRun->GetExitCode();
	}


private:
	CComLoader				m_loader;
	basic_tstring			m_strContainer;
	_lComPtr<IObjectRun>	m_pIObjectRun;
};

#endif

#endif 
