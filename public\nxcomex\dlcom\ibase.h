#ifndef _IBASE_H
#define _IBASE_H

#include <dlcom/unknown.h>

uvStdComNameSpaceBegin

interface IComObjectFrameworkClassFactory : public IComClassFactory
{
	std_method(CreateInstance)(IBase *prot, IBase *punkOuter, REFIID riid, void **ppvObject) =0;
};

// {357C10F2-8A68-4138-BDE5-8C1C3896F7D5}
_DEFINE_IID(IID_IComObjectFrameworkClassFactory,
	0x357c10f2, 0x8a68, 0x4138, 0xbd, 0xe5, 0x8c, 0x1c, 0x38, 0x96, 0xf7, 0xd5);

// {E2247B54-E329-4ca8-8361-6499FDFF98F4}
_DEFINE_GUID(CLSID_ComObjectFrameworkClassFactory,
	0xe2247b54, 0xe329, 0x4ca8, 0x83, 0x61, 0x64, 0x99, 0xfd, 0xff, 0x98, 0xf4);

// {********-A3CB-46fb-9121-3ED22C24CFAD}
_DEFINE_GUID(CLSID_STDCOM_ClassFactory,
	0xe9678781, 0xa3cb, 0x46fb, 0x91, 0x21, 0x3e, 0xd2, 0x2c, 0x24, 0xcf, 0xad);

uvStdComNameSpaceEnd

#endif 
