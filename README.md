# 外设管控

## 项目简介
外设管控是一个用于监控和管理计算机外部设备（特别是USB设备）的安全工具。该工具可以帮助组织控制外部设备的使用，防止未经授权的数据传输和潜在的安全威胁。

## 功能特点
- USB设备实时监控与管理
- 设备策略配置（启用/禁用不同类型设备）
- 支持多种设备类型：存储设备、光驱、便携设备、WiFi网卡等
- 跨平台支持：Windows 和 Linux (aarch64)
- 基于COM组件的插件架构

## 技术架构
- 基于NXCOM/NXCOMEX的COM组件实现
- 使用JSON进行配置和策略解析
- Windows设备管理通过USBManager实现
- 支持ATMON框架集成

## 开发环境
- Windows: Visual Studio 2019
- Linux: 支持ARM64架构

## 使用方法
1. 安装组件
2. 配置设备策略（通过JSON配置文件）
3. 启动监控服务
4. 管理外部设备连接

## 配置示例
```json
{
  "isOpen": true,
  "storage_dev": {"is_enabled": false},
  "cdrom": {"is_enabled": true},
  "portable_dev": {"is_enabled": false},
  "wifi_card": {"is_enabled": false}
}