

/* this ALWAYS GENERATED file contains the IIDs and CLSIDs */

/* link this file in with the server and any clients */


 /* File created by MIDL compiler version 7.00.0555 */
/* at Fri Oct 22 13:25:33 2010
 */
/* Compiler settings for .\runproxy.idl:
    Oicf, W1, Zp8, env=Win32 (32b run), target_arch=X86 7.00.0555 
    protocol : dce , ms_ext, c_ext, robust
    error checks: stub_data 
    VC __declspec() decoration level: 
         __declspec(uuid()), __declspec(selectany), __declspec(novtable)
         DECLSPEC_UUID(), MIDL_INTERFACE()
*/
/* @@MIDL_FILE_HEADING(  ) */

#pragma warning( disable: 4049 )  /* more than 64k source lines */


#ifdef __cplusplus
extern "C"{
#endif 


#include <rpc.h>
#include <rpcndr.h>

#ifdef _MIDL_USE_GUIDDEF_

#ifndef INITGUID
#define INITGUID
#include <guiddef.h>
#undef INITGUID
#else
#include <guiddef.h>
#endif

#define MIDL_DEFINE_GUID(type,name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) \
        DEFINE_GUID(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8)

#else // !_MIDL_USE_GUIDDEF_

#ifndef __IID_DEFINED__
#define __IID_DEFINED__

typedef struct _IID
{
    unsigned long x;
    unsigned short s1;
    unsigned short s2;
    unsigned char  c[8];
} IID;

#endif // __IID_DEFINED__

#ifndef CLSID_DEFINED
#define CLSID_DEFINED
typedef IID CLSID;
#endif // CLSID_DEFINED

#define MIDL_DEFINE_GUID(type,name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) \
        const type name = {l,w1,w2,{b1,b2,b3,b4,b5,b6,b7,b8}}

#endif !_MIDL_USE_GUIDDEF_

MIDL_DEFINE_GUID(IID, IID_IProcessManager,0x80E87323,0xB228,0x4777,0x9E,0x93,0x14,0x0F,0x58,0x6A,0x82,0x40);


MIDL_DEFINE_GUID(IID, LIBID_runproxyLib,0x4AEE03FA,0x5A58,0x4301,0x92,0x3D,0x41,0x1C,0xE3,0x55,0x8B,0x28);


MIDL_DEFINE_GUID(IID, DIID__IProcessManagerEvents,0x7DDF5E93,0xD367,0x43EA,0x88,0x1E,0x63,0xAD,0x5D,0x44,0x8D,0x9F);


MIDL_DEFINE_GUID(CLSID, CLSID_ProcessManager,0x339141C3,0x4B27,0x40EC,0xA4,0x1E,0xEB,0x07,0x14,0xC1,0xF7,0xEA);

#undef MIDL_DEFINE_GUID

#ifdef __cplusplus
}
#endif



