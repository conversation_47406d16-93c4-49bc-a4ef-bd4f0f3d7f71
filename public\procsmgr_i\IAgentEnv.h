#ifndef _IAGENTENV_H_
#define _IAGENTENV_H_
#include <string>

#include "../commproxybase.h"

#ifndef _WL
#if defined(_WIN32) || defined(_WIN64)
#define _WL(x)   L##x
#else
#define _WL(x)   x
#endif
#endif

#ifndef wl_int64
#if defined(_WIN32) || defined(_WIN64)
#define wl_int64 __int64
#else
#define wl_int64 long long int
#endif
#endif

#ifndef wl_stdstring
#if defined(_WIN32) || defined(_WIN64)
#define wl_stdstring  std::wstring
#else
#define wl_stdstring  std::string
#endif
#endif

#define ENV_AGENT_INSTALL_PATH  _WL("env_agent_install_path")

struct IAgentEnvListener
{
	virtual void NotifyEnvUpdated(const wl_stdstring& key, const wl_stdstring& val)=0;
};

#define ENV_KEY_AID				_WL("aid")					//string
#define ENV_KEY_INSTALLPATH		_WL("install_path")         //string

struct IAgentEnv : public ICommBase
{
	virtual void AddListener(IAgentEnvListener* lpPtr) = 0;
	virtual void RelaseListener(IAgentEnvListener* lpPtr) = 0;

	virtual const wl_stdstring GetValue(const wl_stdstring& key) = 0;
	virtual const wl_stdstring GetStringValue(const wl_stdstring& key, const wl_stdstring& def = _WL("")) = 0;
	virtual const int       GetIntValue(const wl_stdstring& key, int def = 0) = 0;
	virtual const wl_int64  GetInt64Value(const wl_stdstring& key, wl_int64 def = 0) = 0;

	virtual bool SetStringValue(const wl_stdstring& key, const wl_stdstring& val) = 0;
	virtual bool SetIntValue(const wl_stdstring& key, int val) = 0;
	virtual bool SetInt64Value(const wl_stdstring& key, wl_int64 val) = 0;
};

_DEF_IID(IAgentEnv, "{6CCE9106-1368-4BDC-9A1A-DFED9F1FD95F}", 0x6CCE9106, 0x1368, 0x4BDC, 0x9A, 0x1A, 0xDF, 0xED, 0x9F, 0x1F, 0xD9, 0x5F);

// {4C311292-94B9-4DFC-9E32-E73CB3F94A42}
_DEF_GUID(CLSID_AgentEnv,
	 0x4c311292, 0x94b9, 0x4dfc,  0x9e, 0x32, 0xe7, 0x3c, 0xb3, 0xf9, 0x4a, 0x42 );

#endif