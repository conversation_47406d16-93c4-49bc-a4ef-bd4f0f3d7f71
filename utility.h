#pragma once
#if defined(_WIN32)
#include <Windows.h>
#endif
#include "acsmon/ukif.h"
#include <string>
#include <vector>
std::string ReadFileToStr(const std::string& path);
std::size_t FindAllMatches(const std::string& input, const std::string& pattern, std::vector<std::string>& matches);
const char* CmdKindToStr(cmd_kind kind);
int SplitString(const std::string& input,
	char delimiter,
	std::vector<std::string>* result);