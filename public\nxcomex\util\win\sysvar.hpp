#ifndef _UTIL_SYSVAR_HPP_
#define _UTIL_SYSVAR_HPP_

typedef HANDLE				_sem_t;
typedef CRITICAL_SECTION	_mutex_t;

typedef struct  os_cond_s{

	int					waiting;
	int					nsignal;
  
    _sem_t				signal_event;
    _sem_t				broadcast_event;
	_mutex_t			lock;

}_cond_t;

typedef union {
    struct {
        unsigned int	num_readers_;
        _mutex_t		num_readers_lock_;
        _sem_t		write_semaphore_;
    } state_;
}_rwlock_t;


#endif