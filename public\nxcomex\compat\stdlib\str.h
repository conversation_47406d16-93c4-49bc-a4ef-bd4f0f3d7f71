#ifndef _COMPAT_STR_H_
#define _COMPAT_STR_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" {
#endif

	char* s_strcpy(
		char * dst,
		size_t size,
		char const* src
	);

	char* s_strncpy(
		char * dest,
		const char * source,
		size_t count
	);

	char* s_strcat(
		char * dst,
		size_t size,
		const char * src
	);

	char* s_strncat(
		char * front,
		const char * back,
		size_t count
	);

	int	s_strcmp(
		const char * src,
		const char * dst
	);

	int	s_stricmp(
		const char * dst,
		const char * src
	);

	int s_strcasecmp(
		const char * dst,
		const char * src
	);

	int s_strncmp(
		const char *first,
		const char *last,
		size_t      count
	);

	char* s_strchr(
		const char *string,
		int ch
	);

	char* s_strrchr(
		const char * string,
		int ch
	);

	size_t s_strspn(
		const char * string,
		const char * control
	);

	size_t s_strcspn(
		const char * string,
		const char * control
	);

	char* s_strpbrk(
		const char * string,
		const char * control
	);

	char* s_strstr(
		const char * str1,
		const char * str2
	);

	size_t	s_strlen(
		const char * str
	);

	size_t	s_strnlen(
		const char * str,
		size_t maxlen
	);


	char* s_strtok(
		char*       str,
		char const* delim,
		char**      context
	);

	char*  s_strset(
		char * string,
		int val
	);

	char*  s_strrev(
		char * string
	);
	
#ifdef	__cplusplus
}
#endif


#endif   


