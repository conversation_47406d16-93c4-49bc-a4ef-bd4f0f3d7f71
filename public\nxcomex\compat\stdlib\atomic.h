#ifndef _COMPAT_ATOMIC_H_
#define _COMPAT_ATOMIC_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef __cplusplus
extern "C"
{
#endif

typedef struct _atomic_s _atomic_t;

struct _atomic_s {
	void*		value;
};

long		atomicadd(long* ptr);
long		atomicdel(long* ptr);
long		atomicinit(long* ptr);
long		atomicuninit(long* ptr);
void		atomic_set(_atomic_t *self, void *value);
void*		atomic_cas(_atomic_t *self, void *cmp, void *value);
void*		atomic_xchg(_atomic_t *self, void *value);


#ifdef __cplusplus
}
#endif

#endif


