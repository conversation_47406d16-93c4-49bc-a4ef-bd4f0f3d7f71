#ifndef _COMPAT_WSTR_H_
#define _COMPAT_WSTR_H_

#include <compat/define.h>
#include <compat/error.h>

#ifdef	__cplusplus
extern "C" {
#endif

#define __ascii_towlower(c)  ( (((c) >= L'A') && ((c) <= L'Z')) ? ((c) - L'A' + L'a') : (c) )
#define __ascii_towupper(c)  ( (((c) >= L'a') && ((c) <= L'z')) ? ((c) - L'a' + L'A') : (c) )


	wchar_t* s_wstrcpy(
		wchar_t * dst,
		size_t size,
		wchar_t const* src
	);

	wchar_t* s_wstrncpy(
		wchar_t * dest,
		const wchar_t * source,
		size_t count
	);

	wchar_t* s_wstrcat(
		wchar_t * dst,
		size_t size,
		const wchar_t * src
	);

	wchar_t*  s_wstrncat(
		wchar_t * front,
		const wchar_t * back,
		size_t count
	);

	int s_wstrcmp(
		const wchar_t * src,
		const wchar_t * dst
	);

	int s_wstricmp(
		const wchar_t * dst,
		const wchar_t * src
	);

	int s_wstrcasecmp(
		const wchar_t * dst,
		const wchar_t * src
	);

	int s_wstrncmp(
		const wchar_t * first,
		const wchar_t * last,
		size_t count
	);

	wchar_t* s_wstrchr(
		const wchar_t * string,
		wchar_t ch
	);

	size_t s_wstrspn(
		const wchar_t* string,
		const wchar_t* control
	);

	size_t s_wstrcspn(
		const wchar_t * string,
		const wchar_t * control
	);

	wchar_t* s_wstrpbrk(
		const wchar_t * string,
		const wchar_t * control
	);

	wchar_t* s_wstrstr(
		const wchar_t * wcs1,
		const wchar_t * wcs2
	);

	size_t s_wstrlen(
		const wchar_t * wcs
	);
	size_t s_wstrnlen (
			const wchar_t *s,
			size_t maxlen
	);

	wchar_t* s_wstrtok(
		wchar_t * string,
		const wchar_t * control,
		wchar_t ** context
	);

	wchar_t* s_wstrset(
		wchar_t * string,
		wchar_t val
	);

	wchar_t* s_wstrrev(
		wchar_t * string
	);



#ifdef	__cplusplus
}
#endif


#endif  

