#ifndef _COM_SENTRY_HPP_
#define _COM_SENTRY_HPP_


#include <util/core.hpp>
#include <utilex/sentry.hpp>

uvStdComNameSpaceBegin


	struct co_interface_sentry
	{
		static void* default_value() 
		{ 
			return 0; 
		}
		template<class _Ptr> static bool equal_to(_Ptr, _Ptr) 
		{
			 return false; 
		}
		template<class _Ptr> static void destroy(_Ptr p) 
		{ 
			if(p)
			{
				p->Release();
			}
		}
	};


	template<class I>
    class ComPtr : public sentry<I*, co_interface_sentry>
	{
    public:
		typedef sentry<I*, co_interface_sentry> base;
		using base::m_p;
	
		ComPtr() : base()
		{

		}
		void attach(I* p) 
		{ 
			base::dispose(); 
			m_p = p; 
		}
	};
	
uvStdComNameSpaceEnd

#define _lComPtr StdCom::ComPtr

#endif 
